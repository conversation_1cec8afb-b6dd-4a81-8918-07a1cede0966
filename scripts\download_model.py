#!/usr/bin/env python3
"""
Script to download and cache MedGemma model
"""

import os
import sys
import logging
from pathlib import Path
from transformers import AutoProcessor, AutoModelForImageTextToText
from huggingface_hub import login, HfApi
import torch

# Add app directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.config import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_huggingface_token():
    """Check if HuggingFace token is available"""
    token = os.getenv('HUGGINGFACE_TOKEN') or os.getenv('HF_TOKEN')
    if not token:
        logger.warning("No HuggingFace token found. You may need to set HUGGINGFACE_TOKEN environment variable.")
        logger.warning("Some models may require authentication.")
        return None
    
    try:
        login(token=token)
        logger.info("Successfully authenticated with HuggingFace")
        return token
    except Exception as e:
        logger.error(f"Failed to authenticate with HuggingFace: {e}")
        return None


def check_model_access(model_name: str, token: str = None):
    """Check if we have access to the model"""
    try:
        api = HfApi(token=token)
        model_info = api.model_info(model_name)
        logger.info(f"Model {model_name} is accessible")
        return True
    except Exception as e:
        logger.error(f"Cannot access model {model_name}: {e}")
        logger.error("You may need to:")
        logger.error("1. Accept the model's license agreement on HuggingFace")
        logger.error("2. Set a valid HUGGINGFACE_TOKEN environment variable")
        logger.error("3. Ensure you have access to the model")
        return False


def download_model(model_name: str, cache_dir: str, token: str = None):
    """Download and cache the model"""
    logger.info(f"Downloading model: {model_name}")
    logger.info(f"Cache directory: {cache_dir}")
    
    # Create cache directory
    Path(cache_dir).mkdir(parents=True, exist_ok=True)
    
    try:
        # Check model access first
        if not check_model_access(model_name, token):
            return False
        
        # Download processor
        logger.info("Downloading processor...")
        processor = AutoProcessor.from_pretrained(
            model_name,
            cache_dir=cache_dir,
            trust_remote_code=True,
            token=token
        )
        logger.info("Processor downloaded successfully")
        
        # Download model
        logger.info("Downloading model...")
        model = AutoModelForImageTextToText.from_pretrained(
            model_name,
            cache_dir=cache_dir,
            torch_dtype=torch.float32,  # Use float32 for CPU
            trust_remote_code=True,
            token=token,
            low_cpu_mem_usage=True
        )
        logger.info("Model downloaded successfully")
        
        # Test model loading
        logger.info("Testing model loading...")
        device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {device}")
        
        if device == "cpu":
            model = model.to(device)
        
        logger.info("Model test successful")
        
        # Clean up memory
        del model
        del processor
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to download model: {e}")
        return False


def get_model_size(cache_dir: str):
    """Get the size of downloaded model files"""
    try:
        total_size = 0
        cache_path = Path(cache_dir)
        
        if cache_path.exists():
            for file_path in cache_path.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        
        # Convert to human readable format
        for unit in ['B', 'KB', 'MB', 'GB']:
            if total_size < 1024.0:
                return f"{total_size:.1f} {unit}"
            total_size /= 1024.0
        
        return f"{total_size:.1f} TB"
        
    except Exception as e:
        logger.error(f"Failed to calculate model size: {e}")
        return "Unknown"


def main():
    """Main function"""
    logger.info("Starting model download...")
    
    # Check HuggingFace token
    token = check_huggingface_token()
    
    # Download model
    success = download_model(
        model_name=settings.MODEL_NAME,
        cache_dir=settings.MODEL_CACHE_DIR,
        token=token
    )
    
    if success:
        model_size = get_model_size(settings.MODEL_CACHE_DIR)
        logger.info(f"Model download completed successfully!")
        logger.info(f"Model size: {model_size}")
        logger.info(f"Cache location: {settings.MODEL_CACHE_DIR}")
    else:
        logger.error("Model download failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
