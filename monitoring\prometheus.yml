# Prometheus configuration for MedGemma AI Chat monitoring

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # MedGemma API metrics
  - job_name: 'medgemma-api'
    static_configs:
      - targets: ['medgemma-api:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Nginx metrics (if nginx-prometheus-exporter is added)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:9113']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Redis metrics (if redis-exporter is added)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Node exporter for system metrics (if added)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
