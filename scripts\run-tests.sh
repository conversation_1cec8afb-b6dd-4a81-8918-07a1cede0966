#!/bin/bash

# Quick test runner for MedGemma AI Chat
# Provides different test options and easy access to test components

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

show_help() {
    echo -e "${BLUE}MedGemma AI Chat - Test Runner${NC}"
    echo "=================================="
    echo
    echo "Usage: $0 [OPTION]"
    echo
    echo "Options:"
    echo "  full          Run complete deployment test suite"
    echo "  build         Test only Docker builds"
    echo "  config        Test only configuration"
    echo "  api           Test only API endpoints"
    echo "  mock          Test mock model functionality"
    echo "  quick         Quick validation (build + config)"
    echo "  cleanup       Clean up test environment"
    echo "  help          Show this help message"
    echo
    echo "Examples:"
    echo "  $0 full       # Complete test suite"
    echo "  $0 quick      # Quick validation"
    echo "  $0 build      # Test builds only"
    echo "  $0 cleanup    # Clean up after tests"
    echo
}

run_full_tests() {
    echo -e "${GREEN}Running complete deployment test suite...${NC}"
    cd "$PROJECT_DIR"
    ./scripts/test-deployment.sh
}

run_build_tests() {
    echo -e "${GREEN}Running build tests only...${NC}"
    cd "$PROJECT_DIR"
    ./scripts/test-deployment.sh --build-only
}

run_config_tests() {
    echo -e "${GREEN}Running configuration tests only...${NC}"
    cd "$PROJECT_DIR"
    ./scripts/test-deployment.sh --config-only
}

run_api_tests() {
    echo -e "${GREEN}Running API tests only...${NC}"
    cd "$PROJECT_DIR"
    ./scripts/test-deployment.sh --api-only
}

run_mock_tests() {
    echo -e "${GREEN}Testing mock model functionality...${NC}"
    cd "$PROJECT_DIR"
    python3 scripts/mock-model.py
}

run_quick_tests() {
    echo -e "${GREEN}Running quick validation tests...${NC}"
    cd "$PROJECT_DIR"
    
    echo -e "${BLUE}1. Testing configuration...${NC}"
    ./scripts/test-deployment.sh --config-only
    
    echo -e "${BLUE}2. Testing builds...${NC}"
    ./scripts/test-deployment.sh --build-only
    
    echo -e "${GREEN}Quick tests completed!${NC}"
}

cleanup_tests() {
    echo -e "${YELLOW}Cleaning up test environment...${NC}"
    cd "$PROJECT_DIR"
    ./scripts/test-deployment.sh --cleanup
}

# Main execution
case "${1:-help}" in
    full)
        run_full_tests
        ;;
    build)
        run_build_tests
        ;;
    config)
        run_config_tests
        ;;
    api)
        run_api_tests
        ;;
    mock)
        run_mock_tests
        ;;
    quick)
        run_quick_tests
        ;;
    cleanup)
        cleanup_tests
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo -e "${RED}Unknown option: $1${NC}"
        echo
        show_help
        exit 1
        ;;
esac
