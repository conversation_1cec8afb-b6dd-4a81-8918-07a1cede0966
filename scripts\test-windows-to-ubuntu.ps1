# MedGemma AI Chat - Windows 11 to Ubuntu 24.04 Compatibility Test
# PowerShell script for testing on Windows 11 with Ubuntu production validation

param(
    [string]$TestType = "full",
    [switch]$SkipBuild = $false,
    [switch]$Verbose = $false
)

# Colors for PowerShell output
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Blue = "Cyan"

# Test counters
$script:TotalTests = 0
$script:PassedTests = 0
$script:FailedTests = 0
$script:TestResults = @()

function Write-TestLog {
    param([string]$Message, [string]$Color = "White")
    $timestamp = Get-Date -Format "HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
}

function Test-Start {
    param([string]$TestName)
    $script:TotalTests++
    Write-TestLog "Starting test: $TestName" $Blue
}

function Test-Pass {
    param([string]$TestName)
    $script:PassedTests++
    $script:TestResults += "✅ PASS: $TestName"
    Write-TestLog "PASSED: $TestName" $Green
}

function Test-Fail {
    param([string]$TestName, [string]$Reason)
    $script:FailedTests++
    $script:TestResults += "❌ FAIL: $TestName - $Reason"
    Write-TestLog "FAILED: $TestName - $Reason" $Red
}

function Test-Prerequisites {
    Write-TestLog "=== Prerequisites Check ===" $Blue
    
    Test-Start "Docker Desktop installation"
    try {
        $dockerVersion = docker --version 2>$null
        if ($dockerVersion) {
            Test-Pass "Docker Desktop installation"
            Write-TestLog "Docker version: $dockerVersion" $Blue
        } else {
            Test-Fail "Docker Desktop installation" "Docker not found or not running"
            return $false
        }
    } catch {
        Test-Fail "Docker Desktop installation" "Docker command failed"
        return $false
    }
    
    Test-Start "Docker Compose availability"
    try {
        $composeVersion = docker-compose --version 2>$null
        if ($composeVersion) {
            Test-Pass "Docker Compose availability"
            Write-TestLog "Docker Compose version: $composeVersion" $Blue
        } else {
            Test-Fail "Docker Compose availability" "Docker Compose not found"
            return $false
        }
    } catch {
        Test-Fail "Docker Compose availability" "Docker Compose command failed"
        return $false
    }
    
    Test-Start "Docker daemon running"
    try {
        docker info | Out-Null
        Test-Pass "Docker daemon running"
    } catch {
        Test-Fail "Docker daemon running" "Docker daemon not accessible"
        return $false
    }
    
    Test-Start "Linux containers mode"
    try {
        $dockerInfo = docker info 2>$null | Select-String "OSType"
        if ($dockerInfo -match "linux") {
            Test-Pass "Linux containers mode"
        } else {
            Test-Fail "Linux containers mode" "Docker not in Linux containers mode"
            Write-TestLog "Please switch Docker Desktop to Linux containers mode" $Yellow
            return $false
        }
    } catch {
        Test-Fail "Linux containers mode" "Could not determine container mode"
        return $false
    }
    
    Test-Start "Project files structure"
    $requiredFiles = @(
        "docker-compose.dev.yml",
        "requirements.txt",
        "app\main.py",
        "frontend\index.html",
        ".env.development"
    )
    
    $missingFiles = @()
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            $missingFiles += $file
        }
    }
    
    if ($missingFiles.Count -eq 0) {
        Test-Pass "Project files structure"
    } else {
        Test-Fail "Project files structure" "Missing files: $($missingFiles -join ', ')"
        return $false
    }
    
    return $true
}

function Test-DependencyResolution {
    Write-TestLog "=== Dependency Resolution Test ===" $Blue
    
    Test-Start "Requirements.txt syntax"
    try {
        if (Test-Path "requirements.txt") {
            $content = Get-Content "requirements.txt"
            $hasConflicts = $false
            
            # Check for Pillow version conflicts
            $pillowLines = $content | Where-Object { $_ -match "pillow" -and $_ -notmatch "^#" }
            if ($pillowLines.Count -gt 1) {
                Test-Fail "Requirements.txt syntax" "Multiple Pillow version specifications found"
                $hasConflicts = $true
            }
            
            # Check for duplicate packages
            $packages = $content | Where-Object { $_ -match "^[a-zA-Z]" } | ForEach-Object { ($_ -split "==|>=|<=|>|<|!=")[0] }
            $duplicates = $packages | Group-Object | Where-Object { $_.Count -gt 1 }
            
            if ($duplicates) {
                Test-Fail "Requirements.txt syntax" "Duplicate packages found: $($duplicates.Name -join ', ')"
                $hasConflicts = $true
            }
            
            if (-not $hasConflicts) {
                Test-Pass "Requirements.txt syntax"
            }
        } else {
            Test-Fail "Requirements.txt syntax" "requirements.txt not found"
        }
    } catch {
        Test-Fail "Requirements.txt syntax" "Error reading requirements.txt"
    }
    
    Test-Start "Docker build dependency test"
    if (-not $SkipBuild) {
        try {
            Write-TestLog "Building test container to validate dependencies..." $Blue
            $buildOutput = docker build -f docker/Dockerfile.medgemma -t medgemma-test . 2>&1
            
            if ($LASTEXITCODE -eq 0) {
                Test-Pass "Docker build dependency test"
                Write-TestLog "Dependencies resolved successfully" $Green
            } else {
                Test-Fail "Docker build dependency test" "Build failed - check dependency conflicts"
                if ($Verbose) {
                    Write-TestLog "Build output: $buildOutput" $Red
                }
            }
        } catch {
            Test-Fail "Docker build dependency test" "Build process failed"
        }
    } else {
        Write-TestLog "Skipping build test (SkipBuild flag set)" $Yellow
    }
}

function Test-CrossPlatformCompatibility {
    Write-TestLog "=== Cross-Platform Compatibility Test ===" $Blue
    
    Test-Start "Ubuntu 24.04 base image compatibility"
    try {
        # Test if we can pull and run Ubuntu 24.04 image
        docker pull ubuntu:24.04 | Out-Null
        $ubuntuTest = docker run --rm ubuntu:24.04 cat /etc/os-release 2>$null
        
        if ($ubuntuTest -match "Ubuntu 24.04") {
            Test-Pass "Ubuntu 24.04 base image compatibility"
        } else {
            Test-Fail "Ubuntu 24.04 base image compatibility" "Could not verify Ubuntu 24.04"
        }
    } catch {
        Test-Fail "Ubuntu 24.04 base image compatibility" "Ubuntu image test failed"
    }
    
    Test-Start "Python 3.11 compatibility"
    try {
        $pythonTest = docker run --rm python:3.11-slim python --version 2>$null
        if ($pythonTest -match "Python 3.11") {
            Test-Pass "Python 3.11 compatibility"
        } else {
            Test-Fail "Python 3.11 compatibility" "Python 3.11 not available"
        }
    } catch {
        Test-Fail "Python 3.11 compatibility" "Python test failed"
    }
    
    Test-Start "File path compatibility"
    # Check for Windows-specific paths that might cause issues
    $dockerFiles = Get-ChildItem -Recurse -Include "Dockerfile*", "docker-compose*.yml"
    $pathIssues = @()
    
    foreach ($file in $dockerFiles) {
        $content = Get-Content $file.FullName
        foreach ($line in $content) {
            if ($line -match "C:\\|\\\\|[A-Z]:\\") {
                $pathIssues += "$($file.Name): $line"
            }
        }
    }
    
    if ($pathIssues.Count -eq 0) {
        Test-Pass "File path compatibility"
    } else {
        Test-Fail "File path compatibility" "Windows-specific paths found: $($pathIssues -join '; ')"
    }
}

function Test-MockFramework {
    Write-TestLog "=== Mock Framework Test ===" $Blue
    
    Test-Start "Mock model script"
    try {
        if (Test-Path "scripts\mock-model.py") {
            # Test if the mock model script is valid Python
            $pythonTest = docker run --rm -v "${PWD}:/app" python:3.11-slim python /app/scripts/mock-model.py 2>&1
            
            if ($LASTEXITCODE -eq 0) {
                Test-Pass "Mock model script"
            } else {
                Test-Fail "Mock model script" "Mock model script has errors"
                if ($Verbose) {
                    Write-TestLog "Python test output: $pythonTest" $Red
                }
            }
        } else {
            Test-Fail "Mock model script" "scripts/mock-model.py not found"
        }
    } catch {
        Test-Fail "Mock model script" "Error testing mock model script"
    }
    
    Test-Start "Test environment configuration"
    try {
        # Create test environment file
        if (Test-Path ".env.development") {
            Copy-Item ".env.development" ".env.test"
            
            # Modify for testing
            $envContent = Get-Content ".env.test"
            $envContent = $envContent -replace "ENVIRONMENT=development", "ENVIRONMENT=test"
            $envContent = $envContent -replace "USE_MOCK_MODEL=false", "USE_MOCK_MODEL=true"
            $envContent | Set-Content ".env.test"
            
            Test-Pass "Test environment configuration"
        } else {
            Test-Fail "Test environment configuration" ".env.development not found"
        }
    } catch {
        Test-Fail "Test environment configuration" "Error creating test environment"
    }
}

function Test-ServiceIntegration {
    Write-TestLog "=== Service Integration Test ===" $Blue
    
    if ($SkipBuild) {
        Write-TestLog "Skipping service integration tests (SkipBuild flag set)" $Yellow
        return
    }
    
    Test-Start "Docker Compose build"
    try {
        Write-TestLog "Building services with docker-compose..." $Blue
        $buildOutput = docker-compose -f docker-compose.dev.yml build 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Test-Pass "Docker Compose build"
        } else {
            Test-Fail "Docker Compose build" "Docker Compose build failed"
            if ($Verbose) {
                Write-TestLog "Build output: $buildOutput" $Red
            }
        }
    } catch {
        Test-Fail "Docker Compose build" "Build process failed"
    }
    
    Test-Start "Service startup test"
    try {
        Write-TestLog "Starting services..." $Blue
        docker-compose -f docker-compose.dev.yml up -d 2>&1 | Out-Null
        
        Start-Sleep -Seconds 10
        
        $services = docker-compose -f docker-compose.dev.yml ps --services
        $runningServices = docker-compose -f docker-compose.dev.yml ps --filter "status=running" --services
        
        if ($runningServices.Count -eq $services.Count) {
            Test-Pass "Service startup test"
        } else {
            Test-Fail "Service startup test" "Not all services started successfully"
        }
    } catch {
        Test-Fail "Service startup test" "Error starting services"
    } finally {
        # Cleanup
        docker-compose -f docker-compose.dev.yml down 2>&1 | Out-Null
    }
}

function Generate-TestReport {
    Write-TestLog "=== Test Summary ===" $Blue
    Write-Host ""
    Write-Host "📊 Test Results Summary" -ForegroundColor $Blue
    Write-Host "=========================="
    Write-Host "Total Tests: $script:TotalTests" -ForegroundColor $Blue
    Write-Host "Passed: $script:PassedTests" -ForegroundColor $Green
    Write-Host "Failed: $script:FailedTests" -ForegroundColor $Red
    Write-Host ""
    
    if ($script:FailedTests -eq 0) {
        Write-Host "🎉 All tests passed!" -ForegroundColor $Green
        Write-Host "Your Windows 11 environment is ready for Ubuntu 24.04 deployment." -ForegroundColor $Green
    } else {
        Write-Host "❌ Some tests failed. Please review the issues above." -ForegroundColor $Red
    }
    
    Write-Host ""
    Write-Host "Detailed Results:" -ForegroundColor $Blue
    foreach ($result in $script:TestResults) {
        if ($result -match "PASS") {
            Write-Host "  $result" -ForegroundColor $Green
        } else {
            Write-Host "  $result" -ForegroundColor $Red
        }
    }
    
    Write-Host ""
    Write-Host "Next Steps:" -ForegroundColor $Blue
    if ($script:FailedTests -eq 0) {
        Write-Host "✅ Push to Ubuntu 24.04 server and run: ./scripts/deploy-dev.sh" -ForegroundColor $Green
        Write-Host "✅ Or test locally with: docker-compose -f docker-compose.dev.yml up -d" -ForegroundColor $Green
    } else {
        Write-Host "🔧 Fix failing tests and run again: .\scripts\test-windows-to-ubuntu.ps1" -ForegroundColor $Yellow
        Write-Host "📚 Check troubleshooting guide: docs\TROUBLESHOOTING.md" -ForegroundColor $Yellow
    }
}

# Main execution
Write-Host ""
Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor $Blue
Write-Host "║        MedGemma AI Chat - Windows 11 to Ubuntu 24.04        ║" -ForegroundColor $Blue
Write-Host "║                    Compatibility Test                       ║" -ForegroundColor $Blue
Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor $Blue
Write-Host ""

Write-TestLog "Starting Windows 11 to Ubuntu 24.04 compatibility test..." $Blue
Write-TestLog "Test type: $TestType" $Blue

# Run test suites based on type
switch ($TestType.ToLower()) {
    "quick" {
        if (Test-Prerequisites) {
            Test-DependencyResolution
            Test-CrossPlatformCompatibility
        }
    }
    "build" {
        if (Test-Prerequisites) {
            Test-DependencyResolution
        }
    }
    "mock" {
        if (Test-Prerequisites) {
            Test-MockFramework
        }
    }
    "full" {
        if (Test-Prerequisites) {
            Test-DependencyResolution
            Test-CrossPlatformCompatibility
            Test-MockFramework
            Test-ServiceIntegration
        }
    }
    default {
        Write-TestLog "Unknown test type: $TestType" $Red
        Write-TestLog "Valid types: quick, build, mock, full" $Yellow
        exit 1
    }
}

Generate-TestReport

# Cleanup
if (Test-Path ".env.test") {
    Remove-Item ".env.test" -Force
}

# Exit with appropriate code
if ($script:FailedTests -eq 0) {
    exit 0
} else {
    exit 1
}
