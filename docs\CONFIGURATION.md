# MedGemma AI Chat - Configuration Guide

This guide explains all configuration options for the MedGemma AI Chat application.

## 📁 Configuration Files

### Primary Configuration
- `.env` - Environment variables (main configuration)
- `docker-compose.yml` - Service orchestration
- `nginx/nginx.conf` - Web server configuration
- `nginx/conf.d/medgemma.conf` - Site-specific configuration

### Application Configuration
- `app/config.py` - Python application settings
- `requirements.txt` - Python dependencies

## 🔧 Environment Variables

### API Configuration

```bash
# API Authentication and Security
API_KEY=your-secret-api-key-change-this-in-production
# Must be at least 32 characters in production
# Used for API authentication

HOST=0.0.0.0
# Host to bind the FastAPI server
# Use 0.0.0.0 to accept connections from any IP

PORT=8000
# Port for the FastAPI server
# Internal port, exposed through nginx

DEBUG=false
# Enable debug mode (true/false)
# Set to false in production

ENVIRONMENT=production
# Environment type: development, testing, production
# Affects logging and security settings
```

### Model Configuration

```bash
MODEL_NAME=google/medgemma-4b-it
# HuggingFace model identifier
# Do not change unless using a different model

MODEL_CACHE_DIR=/app/model_cache
# Directory to cache downloaded model files
# Should be mounted as a volume for persistence

MAX_LENGTH=2048
# Default maximum response length in tokens
# Can be overridden per request (1-4096)

TEMPERATURE=0.7
# Default generation temperature (0.0-2.0)
# Higher values = more creative, lower = more focused

TOP_P=0.9
# Default top-p sampling parameter (0.0-1.0)
# Controls diversity of generated text
```

### HuggingFace Configuration

```bash
HUGGINGFACE_TOKEN=hf_your_token_here
# Required for downloading MedGemma model
# Get from https://huggingface.co/settings/tokens
# Must accept MedGemma license agreement

HF_TOKEN=hf_your_token_here
# Alternative name for HuggingFace token
# Some libraries check for this variable
```

### Performance Configuration

```bash
WORKERS=1
# Number of FastAPI worker processes
# Recommended: 1 for t3.xlarge due to memory constraints

MAX_CONCURRENT_REQUESTS=10
# Maximum concurrent inference requests
# Adjust based on available memory

REQUEST_TIMEOUT=300
# Request timeout in seconds
# Increase for complex image analysis
```

### File Upload Configuration

```bash
UPLOAD_MAX_SIZE=10485760
# Maximum upload size in bytes (10MB default)
# Adjust based on your needs and server capacity

UPLOAD_DIR=/app/uploads
# Directory for temporary file uploads
# Should be mounted as a volume

ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif,image/bmp,image/tiff
# Comma-separated list of allowed MIME types
# Add or remove types as needed
```

### Security Configuration

```bash
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com
# Comma-separated list of allowed CORS origins
# Include all domains that will access the API

ALLOWED_HOSTS=your-domain.com,www.your-domain.com,localhost
# Comma-separated list of allowed hosts
# Include all domains and IPs that will serve the app

RATE_LIMIT_REQUESTS=100
# Number of requests allowed per time window
# Adjust based on expected usage

RATE_LIMIT_WINDOW=60
# Rate limit time window in seconds
# Default: 100 requests per 60 seconds
```

### Database Configuration

```bash
REDIS_URL=redis://redis:6379
# Redis connection URL
# Use service name 'redis' for Docker Compose

REDIS_PASSWORD=your-secure-redis-password
# Redis authentication password
# Use a strong password in production

CONVERSATION_HISTORY_LIMIT=50
# Maximum messages to keep per conversation
# Older messages are automatically removed

CONVERSATION_TTL=86400
# Conversation time-to-live in seconds (24 hours)
# Conversations are deleted after this time
```

### Logging Configuration

```bash
LOG_LEVEL=INFO
# Logging level: DEBUG, INFO, WARNING, ERROR, CRITICAL
# Use INFO or WARNING in production

LOG_FORMAT=json
# Log format: json or text
# JSON is recommended for production monitoring

LOG_DIR=/app/logs
# Directory for log files
# Should be mounted as a volume for persistence
```

### SSL Configuration

```bash
DOMAIN_NAME=your-domain.com
# Your domain name for SSL certificate
# Used by Let's Encrypt and nginx configuration

SSL_EMAIL=<EMAIL>
# Email for Let's Encrypt certificate registration
# Required for automatic certificate renewal

SSL_CERT_PATH=/etc/nginx/ssl/fullchain.pem
# Path to SSL certificate file
# Automatically set by Let's Encrypt

SSL_KEY_PATH=/etc/nginx/ssl/privkey.pem
# Path to SSL private key file
# Automatically set by Let's Encrypt
```

### Monitoring Configuration

```bash
ENABLE_METRICS=true
# Enable Prometheus metrics collection
# Set to false to disable metrics

METRICS_PORT=9090
# Port for Prometheus metrics server
# Internal port for monitoring

HEALTH_CHECK_INTERVAL=30
# Health check interval in seconds
# How often Docker checks service health

HEALTH_CHECK_TIMEOUT=10
# Health check timeout in seconds
# How long to wait for health check response
```

## 🐳 Docker Compose Configuration

### Service Configuration

```yaml
# Memory limits for MedGemma service
deploy:
  resources:
    limits:
      memory: 14G        # Maximum memory usage
    reservations:
      memory: 12G        # Reserved memory

# Volume mounts
volumes:
  - model_cache:/app/model_cache      # Model files
  - upload_data:/app/uploads          # Uploaded files
  - logs:/app/logs                    # Log files
```

### Network Configuration

```yaml
networks:
  medgemma-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16       # Internal network subnet
```

## 🌐 Nginx Configuration

### Main Configuration (`nginx/nginx.conf`)

```nginx
# Worker processes
worker_processes auto;              # Auto-detect CPU cores

# Connection limits
worker_connections 1024;            # Connections per worker

# Buffer sizes
client_max_body_size 10m;          # Maximum request body size
client_body_buffer_size 128k;      # Body buffer size

# Timeouts
client_body_timeout 12;            # Body read timeout
client_header_timeout 12;          # Header read timeout
keepalive_timeout 15;              # Keep-alive timeout
```

### Site Configuration (`nginx/conf.d/medgemma.conf`)

```nginx
# Rate limiting
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=upload_limit:10m rate=2r/s;

# Upstream configuration
upstream medgemma_api {
    server medgemma-api:8000;       # Backend service
    keepalive 32;                   # Keep-alive connections
}
```

## ⚙️ Application Configuration

### Python Settings (`app/config.py`)

The application uses Pydantic settings for configuration management:

```python
class Settings(BaseSettings):
    # All environment variables are automatically loaded
    # Validation is performed on startup
    # Type conversion is handled automatically
    
    class Config:
        env_file = ".env"           # Load from .env file
        case_sensitive = True       # Environment variables are case-sensitive
```

### Model Configuration

```python
# Model loading settings
MODEL_CACHE_DIR: str = "/app/model_cache"
TORCH_DTYPE: str = "float32"        # Use float32 for CPU inference
DEVICE_MAP: str = "auto"            # Automatic device mapping
LOW_CPU_MEM_USAGE: bool = True      # Optimize for low memory
```

## 🔒 Security Configuration

### API Security

```bash
# Strong API key (minimum 32 characters)
API_KEY=abcdef1234567890abcdef1234567890abcdef12

# CORS configuration
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Allowed hosts
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
```

### Nginx Security Headers

```nginx
# Security headers (automatically added)
add_header X-Frame-Options "DENY" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
```

### Rate Limiting

```nginx
# API rate limiting
limit_req zone=api_limit burst=20 nodelay;

# Upload rate limiting
limit_req zone=upload_limit burst=5 nodelay;

# Connection limiting
limit_conn conn_limit_per_ip 10;
```

## 📊 Monitoring Configuration

### Prometheus Metrics

```yaml
# Prometheus configuration
monitoring:
  image: prom/prometheus:latest
  ports:
    - "9090:9090"
  volumes:
    - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
```

### Health Checks

```yaml
# Docker health check
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 120s
```

## 🔄 Environment-Specific Configurations

### Development Environment

```bash
# .env.development
DEBUG=true
LOG_LEVEL=DEBUG
CORS_ORIGINS=*
MODEL_NAME=microsoft/DialoGPT-small  # Smaller model for testing
```

### Production Environment

```bash
# .env.production
DEBUG=false
LOG_LEVEL=INFO
API_KEY=your-super-secure-32-char-api-key
CORS_ORIGINS=https://yourdomain.com
REDIS_PASSWORD=your-secure-redis-password
```

### Testing Environment

```bash
# .env.testing
DEBUG=true
LOG_LEVEL=DEBUG
REDIS_URL=redis://localhost:6379/1  # Different Redis DB
MODEL_NAME=microsoft/DialoGPT-small
```

## 🔧 Advanced Configuration

### Custom Model Configuration

```bash
# Use a different model
MODEL_NAME=your-custom-model/model-name
MODEL_CACHE_DIR=/custom/cache/path

# Custom generation parameters
MAX_LENGTH=4096
TEMPERATURE=0.5
TOP_P=0.95
```

### Performance Tuning

```bash
# Increase concurrent requests (if you have more memory)
MAX_CONCURRENT_REQUESTS=20

# Adjust timeouts
REQUEST_TIMEOUT=600
HEALTH_CHECK_TIMEOUT=30

# Memory optimization
WORKERS=2  # Only if you have sufficient memory
```

### Custom SSL Configuration

```bash
# Use custom SSL certificates
SSL_CERT_PATH=/custom/path/to/cert.pem
SSL_KEY_PATH=/custom/path/to/key.pem

# Disable Let's Encrypt
USE_LETSENCRYPT=false
```

## ✅ Configuration Validation

### Startup Validation

The application validates configuration on startup:

```python
# Automatic validation in app/config.py
@validator('API_KEY')
def validate_api_key_production(cls, v):
    if v == "your-secret-api-key":
        raise ValueError('API_KEY must be changed in production')
    return v

@validator('TEMPERATURE')
def validate_temperature(cls, v):
    if not 0.0 <= v <= 2.0:
        raise ValueError('TEMPERATURE must be between 0.0 and 2.0')
    return v
```

### Configuration Testing

```bash
# Test configuration
docker-compose config

# Validate environment variables
docker-compose run --rm medgemma-api python -c "
from app.config import settings
print('Configuration valid!')
print(f'Model: {settings.MODEL_NAME}')
print(f'Debug: {settings.DEBUG}')
"
```

## 📚 Configuration Examples

### Minimal Production Configuration

```bash
# Essential production settings
API_KEY=your-32-character-secure-api-key
DOMAIN_NAME=yourdomain.com
SSL_EMAIL=<EMAIL>
HUGGINGFACE_TOKEN=hf_your_token_here
REDIS_PASSWORD=your-secure-redis-password
CORS_ORIGINS=https://yourdomain.com
ALLOWED_HOSTS=yourdomain.com
DEBUG=false
LOG_LEVEL=INFO
```

### High-Performance Configuration

```bash
# Optimized for performance
MAX_CONCURRENT_REQUESTS=15
REQUEST_TIMEOUT=600
WORKERS=1
UPLOAD_MAX_SIZE=20971520
CONVERSATION_HISTORY_LIMIT=100
RATE_LIMIT_REQUESTS=200
```

### Security-Focused Configuration

```bash
# Enhanced security
API_KEY=very-long-and-secure-api-key-with-64-characters-minimum
RATE_LIMIT_REQUESTS=50
RATE_LIMIT_WINDOW=60
UPLOAD_MAX_SIZE=5242880
ALLOWED_IMAGE_TYPES=image/jpeg,image/png
CONVERSATION_TTL=3600
LOG_LEVEL=WARNING
```
