# MedGemma AI Chat - Development Setup Guide

This guide helps you set up the MedGemma AI Chat application for development with dynamic IP addresses, without requiring domain names or SSL certificates.

## 🎯 Development vs Production

### Development Setup (This Guide)
- ✅ Works with dynamic IP addresses
- ✅ HTTP only (no SSL required)
- ✅ No domain name needed
- ✅ Relaxed security for testing
- ✅ Quick setup and testing

### Production Setup
- 🔒 Requires domain name and SSL
- 🔒 HTTPS with proper certificates
- 🔒 Enhanced security measures
- 🔒 Production-grade monitoring
- 📚 See [DEPLOYMENT.md](DEPLOYMENT.md) when ready

## 🚀 Quick Development Setup

### Option 1: Automated Development Deployment (Recommended)

```bash
# Clone the repository
git clone https://github.com/your-username/docker-medgemma-fastapi.git
cd docker-medgemma-fastapi

# Run the development deployment script
chmod +x scripts/deploy-dev.sh
./scripts/deploy-dev.sh
```

The script will:
1. Check system requirements
2. Install Docker and Docker Compose
3. Configure development environment
4. Deploy services with HTTP-only configuration
5. Display your application URL with current IP

### Option 2: Manual Development Setup

```bash
# 1. Copy development configuration
cp .env.development .env

# 2. Edit configuration with your HuggingFace token
nano .env
# Set: HUGGINGFACE_TOKEN=your_token_here

# 3. Build and start development services
docker-compose -f docker-compose.dev.yml build
docker-compose -f docker-compose.dev.yml up -d

# 4. Get your public IP
curl http://checkip.amazonaws.com/

# 5. Access your application at http://YOUR_PUBLIC_IP
```

## 📋 Prerequisites

### Required
- AWS EC2 t3.xlarge instance (4 vCPU, 16 GB RAM)
- Ubuntu 24.04 LTS
- HuggingFace account with MedGemma access
- HuggingFace token with read permissions

### Not Required for Development
- ❌ Domain name
- ❌ SSL certificates
- ❌ Elastic IP address
- ❌ DNS configuration

## 🔧 Development Configuration

### Environment Variables (.env.development)

Key differences from production:

```bash
# Development mode
ENVIRONMENT=development
DEBUG=true

# No SSL required
USE_SSL=false
DOMAIN_NAME=localhost

# Relaxed security
CORS_ORIGINS=*
ALLOWED_HOSTS=*

# Development API key
API_KEY=dev-api-key-for-testing-only

# Relaxed rate limiting
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=60
```

### Docker Compose (docker-compose.dev.yml)

Development-specific features:
- HTTP-only nginx configuration
- Relaxed CORS policies
- Development-friendly rate limits
- Optional source code mounting for live reload

## 🌐 Accessing Your Application

### Finding Your IP Address

```bash
# Get your current public IP
curl http://checkip.amazonaws.com/

# Or use the AWS CLI
aws ec2 describe-instances --instance-ids $(curl -s http://***************/latest/meta-data/instance-id) --query 'Reservations[0].Instances[0].PublicIpAddress' --output text
```

### Application URLs

Replace `YOUR_IP` with your actual public IP:

- **Web Interface**: `http://YOUR_IP`
- **API Health**: `http://YOUR_IP/api/health`
- **API Documentation**: `http://YOUR_IP/docs` (if DEBUG=true)
- **Prometheus Metrics**: `http://YOUR_IP:9090`

### Testing the API

```bash
# Replace YOUR_IP with your actual IP
export API_URL="http://YOUR_IP/api"
export API_KEY="dev-api-key-for-testing-only"

# Test health endpoint
curl "$API_URL/health"

# Test chat endpoint
curl -X POST "$API_URL/chat" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{"message": "Hello, how are you?"}'

# Test image upload (with actual image file)
curl -X POST "$API_URL/analyze-image" \
  -H "Authorization: Bearer $API_KEY" \
  -F "message=Analyze this image" \
  -F "image=@test-image.jpg"
```

## 🔄 Managing Dynamic IP Changes

### When Your IP Changes

If your EC2 instance restarts and gets a new IP:

```bash
# 1. Get new IP
NEW_IP=$(curl -s http://checkip.amazonaws.com/)
echo "New IP: $NEW_IP"

# 2. Services should still work automatically
# 3. Update your bookmarks/scripts with new IP
# 4. Test the application
curl "http://$NEW_IP/api/health"
```

### Automation Script

Create a script to get your current access URL:

```bash
#!/bin/bash
# save as get-app-url.sh
PUBLIC_IP=$(curl -s http://checkip.amazonaws.com/)
echo "Application URL: http://$PUBLIC_IP"
echo "API URL: http://$PUBLIC_IP/api"
echo "Health Check: http://$PUBLIC_IP/api/health"
```

## 🛠️ Development Workflow

### Starting Services

```bash
# Start all services
docker-compose -f docker-compose.dev.yml up -d

# Start specific service
docker-compose -f docker-compose.dev.yml up -d medgemma-api

# View logs
docker-compose -f docker-compose.dev.yml logs -f
```

### Stopping Services

```bash
# Stop all services
docker-compose -f docker-compose.dev.yml down

# Stop and remove volumes (clean slate)
docker-compose -f docker-compose.dev.yml down -v
```

### Updating Code

```bash
# Rebuild after code changes
docker-compose -f docker-compose.dev.yml build medgemma-api
docker-compose -f docker-compose.dev.yml up -d medgemma-api

# Or rebuild everything
docker-compose -f docker-compose.dev.yml build
docker-compose -f docker-compose.dev.yml up -d
```

## 🔍 Development Debugging

### Viewing Logs

```bash
# All services
docker-compose -f docker-compose.dev.yml logs -f

# Specific service
docker-compose -f docker-compose.dev.yml logs -f medgemma-api
docker-compose -f docker-compose.dev.yml logs -f nginx-dev

# Last 100 lines
docker-compose -f docker-compose.dev.yml logs --tail=100 medgemma-api
```

### Checking Service Status

```bash
# Service status
docker-compose -f docker-compose.dev.yml ps

# Resource usage
docker stats

# Container inspection
docker inspect medgemma-api-dev
```

### Accessing Containers

```bash
# Access API container
docker-compose -f docker-compose.dev.yml exec medgemma-api bash

# Access nginx container
docker-compose -f docker-compose.dev.yml exec nginx-dev sh

# Access Redis
docker-compose -f docker-compose.dev.yml exec redis redis-cli
```

## 🧪 Testing in Development

### Frontend Testing

1. Open `http://YOUR_IP` in browser
2. Test chat functionality
3. Test image upload
4. Check browser console for errors
5. Test on mobile devices

### API Testing

```bash
# Health check
curl "http://YOUR_IP/api/health"

# Chat test
curl -X POST "http://YOUR_IP/api/chat" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer dev-api-key-for-testing-only" \
  -d '{"message": "Test message"}'

# Image analysis test
curl -X POST "http://YOUR_IP/api/analyze-image" \
  -H "Authorization: Bearer dev-api-key-for-testing-only" \
  -F "message=Test image analysis" \
  -F "image=@sample-image.jpg"
```

### Load Testing

```bash
# Simple load test with curl
for i in {1..10}; do
  curl -X POST "http://YOUR_IP/api/chat" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer dev-api-key-for-testing-only" \
    -d "{\"message\": \"Test message $i\"}" &
done
wait
```

## 🔄 Migration to Production

When you're ready to move to production:

### 1. Get a Domain Name
- Register a domain name
- Point DNS to your server

### 2. Get an Elastic IP (Recommended)
- Allocate an Elastic IP in AWS
- Associate it with your instance

### 3. Switch to Production Configuration

```bash
# Stop development services
docker-compose -f docker-compose.dev.yml down

# Copy production environment template
cp .env.example .env

# Edit production configuration
nano .env
# Set your domain, SSL email, secure API key, etc.

# Deploy production services
docker-compose build
docker-compose up -d

# Set up SSL certificate
docker-compose run --rm certbot certonly --webroot \
  --webroot-path=/var/www/certbot \
  --email <EMAIL> \
  --agree-tos -d your-domain.com
```

### 4. Follow Production Guide
- Complete setup using [DEPLOYMENT.md](DEPLOYMENT.md)
- Implement production security measures
- Set up monitoring and backups

## 🚨 Development Limitations

### Security Warnings
- ⚠️ HTTP only (no encryption)
- ⚠️ Relaxed CORS policies
- ⚠️ Development API keys
- ⚠️ No rate limiting protection
- ⚠️ Debug mode enabled

### Performance Considerations
- 🔧 Not optimized for high load
- 🔧 Verbose logging enabled
- 🔧 No CDN or caching
- 🔧 Single instance only

### Network Limitations
- 🌐 IP address changes on restart
- 🌐 No load balancing
- 🌐 No failover capability

## 📞 Development Support

### Common Issues

1. **Can't access application**
   - Check your security group allows port 80
   - Verify your public IP hasn't changed
   - Check service status: `docker-compose -f docker-compose.dev.yml ps`

2. **Model download fails**
   - Verify HuggingFace token is correct
   - Check you've accepted MedGemma license
   - Monitor logs: `docker-compose -f docker-compose.dev.yml logs -f medgemma-api`

3. **Out of memory errors**
   - Ensure you're using t3.xlarge or larger
   - Check memory usage: `free -h`
   - Consider adding swap space

### Getting Help

- Check logs for errors
- Review [TROUBLESHOOTING.md](TROUBLESHOOTING.md)
- Test with curl commands
- Verify environment configuration

## 🎉 Development Setup Complete!

Your development environment should now be running at `http://YOUR_PUBLIC_IP`

**Remember**: This is for development only. When ready for production, follow the [production deployment guide](DEPLOYMENT.md) for a secure, domain-based setup with SSL certificates.
