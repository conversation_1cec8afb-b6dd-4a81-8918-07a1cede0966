"""
Configuration settings for MedGemma API
"""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field, validator


class Settings(BaseSettings):
    """Application settings"""
    
    # API Configuration
    API_KEY: str = Field(default="your-secret-api-key", description="API authentication key")
    HOST: str = Field(default="0.0.0.0", description="Host to bind the server")
    PORT: int = Field(default=8000, description="Port to bind the server")
    DEBUG: bool = Field(default=False, description="Enable debug mode")
    
    # Model Configuration
    MODEL_NAME: str = Field(default="google/medgemma-4b-it", description="HuggingFace model name")
    MODEL_CACHE_DIR: str = Field(default="/app/model_cache", description="Model cache directory")
    MAX_LENGTH: int = Field(default=2048, description="Maximum response length")
    TEMPERATURE: float = Field(default=0.7, description="Default temperature for generation")
    TOP_P: float = Field(default=0.9, description="Default top_p for generation")
    
    # Performance Configuration
    WORKERS: int = Field(default=1, description="Number of worker processes")
    MAX_CONCURRENT_REQUESTS: int = Field(default=10, description="Maximum concurrent requests")
    REQUEST_TIMEOUT: int = Field(default=300, description="Request timeout in seconds")
    
    # File Upload Configuration
    UPLOAD_MAX_SIZE: int = Field(default=10485760, description="Maximum upload size in bytes (10MB)")
    UPLOAD_DIR: str = Field(default="/app/uploads", description="Upload directory")
    ALLOWED_IMAGE_TYPES: List[str] = Field(
        default=["image/jpeg", "image/png", "image/gif", "image/bmp", "image/tiff"],
        description="Allowed image MIME types"
    )
    
    # Security Configuration
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost", "https://localhost"],
        description="Allowed CORS origins"
    )
    ALLOWED_HOSTS: List[str] = Field(
        default=["localhost", "127.0.0.1"],
        description="Allowed hosts"
    )
    
    # Database Configuration
    REDIS_URL: str = Field(default="redis://redis:6379", description="Redis connection URL")
    REDIS_PASSWORD: Optional[str] = Field(default=None, description="Redis password")
    CONVERSATION_HISTORY_LIMIT: int = Field(default=50, description="Maximum conversation history length")
    CONVERSATION_TTL: int = Field(default=86400, description="Conversation TTL in seconds (24 hours)")
    
    # Logging Configuration
    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    LOG_FORMAT: str = Field(default="json", description="Log format (json or text)")
    LOG_DIR: str = Field(default="/app/logs", description="Log directory")
    
    # Monitoring Configuration
    ENABLE_METRICS: bool = Field(default=True, description="Enable Prometheus metrics")
    METRICS_PORT: int = Field(default=9090, description="Metrics server port")
    
    # SSL Configuration
    SSL_CERT_PATH: Optional[str] = Field(default=None, description="SSL certificate path")
    SSL_KEY_PATH: Optional[str] = Field(default=None, description="SSL private key path")
    
    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = Field(default=100, description="Rate limit requests per minute")
    RATE_LIMIT_WINDOW: int = Field(default=60, description="Rate limit window in seconds")
    
    # Health Check Configuration
    HEALTH_CHECK_INTERVAL: int = Field(default=30, description="Health check interval in seconds")
    HEALTH_CHECK_TIMEOUT: int = Field(default=10, description="Health check timeout in seconds")
    
    @validator('CORS_ORIGINS', pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',')]
        return v
    
    @validator('ALLOWED_HOSTS', pre=True)
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(',')]
        return v
    
    @validator('ALLOWED_IMAGE_TYPES', pre=True)
    def parse_allowed_image_types(cls, v):
        if isinstance(v, str):
            return [mime_type.strip() for mime_type in v.split(',')]
        return v
    
    @validator('LOG_LEVEL')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'LOG_LEVEL must be one of {valid_levels}')
        return v.upper()
    
    @validator('TEMPERATURE')
    def validate_temperature(cls, v):
        if not 0.0 <= v <= 2.0:
            raise ValueError('TEMPERATURE must be between 0.0 and 2.0')
        return v
    
    @validator('TOP_P')
    def validate_top_p(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError('TOP_P must be between 0.0 and 1.0')
        return v
    
    @validator('UPLOAD_MAX_SIZE')
    def validate_upload_max_size(cls, v):
        if v <= 0:
            raise ValueError('UPLOAD_MAX_SIZE must be positive')
        # Limit to 100MB
        if v > 104857600:
            raise ValueError('UPLOAD_MAX_SIZE cannot exceed 100MB')
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings"""
    return settings


# Environment-specific configurations
class DevelopmentSettings(Settings):
    """Development environment settings"""
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"
    CORS_ORIGINS: List[str] = ["*"]


class ProductionSettings(Settings):
    """Production environment settings"""
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    
    @validator('API_KEY')
    def validate_api_key_production(cls, v):
        if v == "your-secret-api-key":
            raise ValueError('API_KEY must be set to a secure value in production')
        if len(v) < 32:
            raise ValueError('API_KEY must be at least 32 characters long in production')
        return v


class TestingSettings(Settings):
    """Testing environment settings"""
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"
    MODEL_NAME: str = "microsoft/DialoGPT-small"  # Smaller model for testing
    REDIS_URL: str = "redis://localhost:6379/1"  # Different DB for testing


def get_environment_settings() -> Settings:
    """Get environment-specific settings"""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    elif env == "testing":
        return TestingSettings()
    else:
        return DevelopmentSettings()
