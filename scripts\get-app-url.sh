#!/bin/bash

# Get current application URLs for development
# This script helps you find your current access URLs when using dynamic IPs

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}=== MedGemma AI Chat - Current Access URLs ===${NC}"
echo

# Get public IP
echo -e "${YELLOW}Detecting public IP address...${NC}"
PUBLIC_IP=$(curl -s --max-time 5 http://checkip.amazonaws.com/ 2>/dev/null || \
            curl -s --max-time 5 http://ipv4.icanhazip.com/ 2>/dev/null || \
            curl -s --max-time 5 http://ifconfig.me/ip 2>/dev/null || \
            echo "Unable to determine")

if [[ "$PUBLIC_IP" == "Unable to determine" ]]; then
    echo -e "${YELLOW}Could not determine public IP automatically.${NC}"
    echo "You can find your IP in the AWS EC2 console or by running:"
    echo "  aws ec2 describe-instances --instance-ids \$(curl -s http://***************/latest/meta-data/instance-id) --query 'Reservations[0].Instances[0].PublicIpAddress' --output text"
    exit 1
fi

echo -e "${GREEN}Public IP: $PUBLIC_IP${NC}"
echo

# Display URLs
echo -e "${BLUE}=== Application URLs ===${NC}"
echo -e "🌐 Web Interface:     ${GREEN}http://$PUBLIC_IP${NC}"
echo -e "🔧 API Base URL:      ${GREEN}http://$PUBLIC_IP/api${NC}"
echo -e "❤️  Health Check:     ${GREEN}http://$PUBLIC_IP/api/health${NC}"
echo -e "📊 Prometheus:        ${GREEN}http://$PUBLIC_IP:9090${NC}"
echo

# Test if services are running
echo -e "${BLUE}=== Service Status ===${NC}"
if curl -s --max-time 5 "http://localhost/api/health" >/dev/null 2>&1; then
    echo -e "✅ API Service: ${GREEN}Running${NC}"
else
    echo -e "❌ API Service: ${YELLOW}Not responding${NC}"
    echo "   Try: docker-compose -f docker-compose.dev.yml ps"
fi

if curl -s --max-time 5 "http://localhost" >/dev/null 2>&1; then
    echo -e "✅ Web Interface: ${GREEN}Running${NC}"
else
    echo -e "❌ Web Interface: ${YELLOW}Not responding${NC}"
    echo "   Try: docker-compose -f docker-compose.dev.yml logs nginx-dev"
fi

echo

# Display quick test commands
echo -e "${BLUE}=== Quick Test Commands ===${NC}"
echo "Test health endpoint:"
echo -e "  ${GREEN}curl http://$PUBLIC_IP/api/health${NC}"
echo
echo "Test chat API:"
echo -e "  ${GREEN}curl -X POST http://$PUBLIC_IP/api/chat \\${NC}"
echo -e "    ${GREEN}-H 'Content-Type: application/json' \\${NC}"
echo -e "    ${GREEN}-H 'Authorization: Bearer dev-api-key-for-testing-only' \\${NC}"
echo -e "    ${GREEN}-d '{\"message\": \"Hello, how are you?\"}'${NC}"
echo

# Check if running in development mode
if [[ -f .env ]] && grep -q "ENVIRONMENT=development" .env; then
    echo -e "${YELLOW}⚠️  Running in DEVELOPMENT mode${NC}"
    echo "   - HTTP only (no SSL)"
    echo "   - Development API key in use"
    echo "   - Relaxed security settings"
    echo
    echo -e "${BLUE}For production deployment, see: docs/DEPLOYMENT.md${NC}"
fi

# Save URLs to file for easy access
cat > current-urls.txt << EOF
MedGemma AI Chat - Current URLs
Generated: $(date)

Public IP: $PUBLIC_IP

Application URLs:
- Web Interface: http://$PUBLIC_IP
- API Base URL: http://$PUBLIC_IP/api
- Health Check: http://$PUBLIC_IP/api/health
- Prometheus: http://$PUBLIC_IP:9090

Test Commands:
curl http://$PUBLIC_IP/api/health
curl -X POST http://$PUBLIC_IP/api/chat -H 'Content-Type: application/json' -H 'Authorization: Bearer dev-api-key-for-testing-only' -d '{"message": "Hello"}'
EOF

echo -e "${GREEN}URLs saved to: current-urls.txt${NC}"
