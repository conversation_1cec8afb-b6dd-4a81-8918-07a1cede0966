"""
Custom exceptions for MedGemma API
"""

from typing import Optional, Dict, Any
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
import structlog

logger = structlog.get_logger()


class MedGemmaException(Exception):
    """Base exception for MedGemma API"""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        status_code: int = 500
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.status_code = status_code
        super().__init__(self.message)


class ModelNotLoadedException(MedGemmaException):
    """Exception raised when model is not loaded"""
    
    def __init__(self, message: str = "Model not loaded"):
        super().__init__(
            message=message,
            error_code="MODEL_NOT_LOADED",
            status_code=503
        )


class ModelInferenceException(MedGemmaException):
    """Exception raised during model inference"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="MODEL_INFERENCE_ERROR",
            details=details,
            status_code=500
        )


class InvalidInputException(MedGemmaException):
    """Exception raised for invalid input"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="INVALID_INPUT",
            details=details,
            status_code=400
        )


class ConversationNotFoundException(MedGemmaException):
    """Exception raised when conversation is not found"""
    
    def __init__(self, conversation_id: str):
        super().__init__(
            message=f"Conversation {conversation_id} not found",
            error_code="CONVERSATION_NOT_FOUND",
            details={"conversation_id": conversation_id},
            status_code=404
        )


class FileTooLargeException(MedGemmaException):
    """Exception raised when uploaded file is too large"""
    
    def __init__(self, file_size: int, max_size: int):
        super().__init__(
            message=f"File size {file_size} bytes exceeds maximum {max_size} bytes",
            error_code="FILE_TOO_LARGE",
            details={"file_size": file_size, "max_size": max_size},
            status_code=413
        )


class UnsupportedFileTypeException(MedGemmaException):
    """Exception raised for unsupported file types"""
    
    def __init__(self, file_type: str, supported_types: list):
        super().__init__(
            message=f"File type {file_type} not supported",
            error_code="UNSUPPORTED_FILE_TYPE",
            details={"file_type": file_type, "supported_types": supported_types},
            status_code=400
        )


class RateLimitExceededException(MedGemmaException):
    """Exception raised when rate limit is exceeded"""
    
    def __init__(self, limit: int, window: int, reset_time: str):
        super().__init__(
            message=f"Rate limit of {limit} requests per {window} seconds exceeded",
            error_code="RATE_LIMIT_EXCEEDED",
            details={"limit": limit, "window": window, "reset_time": reset_time},
            status_code=429
        )


class AuthenticationException(MedGemmaException):
    """Exception raised for authentication errors"""
    
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_FAILED",
            status_code=401
        )


class AuthorizationException(MedGemmaException):
    """Exception raised for authorization errors"""
    
    def __init__(self, message: str = "Access denied"):
        super().__init__(
            message=message,
            error_code="ACCESS_DENIED",
            status_code=403
        )


class ServiceUnavailableException(MedGemmaException):
    """Exception raised when service is unavailable"""
    
    def __init__(self, message: str = "Service temporarily unavailable"):
        super().__init__(
            message=message,
            error_code="SERVICE_UNAVAILABLE",
            status_code=503
        )


class ConfigurationException(MedGemmaException):
    """Exception raised for configuration errors"""
    
    def __init__(self, message: str, config_key: Optional[str] = None):
        details = {"config_key": config_key} if config_key else None
        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            details=details,
            status_code=500
        )


class DatabaseException(MedGemmaException):
    """Exception raised for database errors"""
    
    def __init__(self, message: str, operation: Optional[str] = None):
        details = {"operation": operation} if operation else None
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            details=details,
            status_code=500
        )


class ValidationException(MedGemmaException):
    """Exception raised for validation errors"""
    
    def __init__(self, message: str, field: Optional[str] = None, value: Optional[Any] = None):
        details = {}
        if field:
            details["field"] = field
        if value is not None:
            details["value"] = str(value)
        
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details=details if details else None,
            status_code=422
        )


class TimeoutException(MedGemmaException):
    """Exception raised for timeout errors"""
    
    def __init__(self, message: str = "Operation timed out", timeout: Optional[float] = None):
        details = {"timeout": timeout} if timeout else None
        super().__init__(
            message=message,
            error_code="TIMEOUT_ERROR",
            details=details,
            status_code=408
        )


class ResourceNotFoundException(MedGemmaException):
    """Exception raised when a resource is not found"""
    
    def __init__(self, resource_type: str, resource_id: str):
        super().__init__(
            message=f"{resource_type} with ID {resource_id} not found",
            error_code="RESOURCE_NOT_FOUND",
            details={"resource_type": resource_type, "resource_id": resource_id},
            status_code=404
        )


class ConflictException(MedGemmaException):
    """Exception raised for conflict errors"""
    
    def __init__(self, message: str, resource_id: Optional[str] = None):
        details = {"resource_id": resource_id} if resource_id else None
        super().__init__(
            message=message,
            error_code="CONFLICT",
            details=details,
            status_code=409
        )


# Exception handlers
async def handle_medgemma_exception(request: Request, exc: MedGemmaException) -> JSONResponse:
    """Handle MedGemma exceptions"""
    logger.error(
        "MedGemma exception occurred",
        error=exc.message,
        error_code=exc.error_code,
        details=exc.details,
        status_code=exc.status_code,
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.message,
            "error_code": exc.error_code,
            "details": exc.details,
            "timestamp": "2024-01-01T00:00:00Z"  # You might want to use actual timestamp
        }
    )


async def handle_validation_exception(request: Request, exc: Exception) -> JSONResponse:
    """Handle validation exceptions"""
    logger.error(
        "Validation exception occurred",
        error=str(exc),
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=422,
        content={
            "error": "Validation error",
            "error_code": "VALIDATION_ERROR",
            "details": {"message": str(exc)},
            "timestamp": "2024-01-01T00:00:00Z"
        }
    )


async def handle_http_exception(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle HTTP exceptions"""
    logger.error(
        "HTTP exception occurred",
        error=exc.detail,
        status_code=exc.status_code,
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "error_code": "HTTP_ERROR",
            "details": None,
            "timestamp": "2024-01-01T00:00:00Z"
        }
    )


async def handle_generic_exception(request: Request, exc: Exception) -> JSONResponse:
    """Handle generic exceptions"""
    logger.error(
        "Unexpected exception occurred",
        error=str(exc),
        exception_type=type(exc).__name__,
        path=request.url.path,
        method=request.method,
        exc_info=True
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "error_code": "INTERNAL_ERROR",
            "details": {"message": "An unexpected error occurred"},
            "timestamp": "2024-01-01T00:00:00Z"
        }
    )
