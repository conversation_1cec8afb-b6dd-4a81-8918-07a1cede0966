FROM python:3.11-slim

ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    USE_MOCK_MODEL=true \
    ENVIRONMENT=test

RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Install basic dependencies without requirements.txt
RUN pip install --no-cache-dir \
    fastapi==0.104.1 \
    uvicorn[standard]==0.24.0 \
    redis==5.0.1 \
    pydantic==2.5.0 \
    python-multipart==0.0.6 \
    aiofiles==23.2.1 \
    structlog==23.2.0 \
    pillow==10.1.0

# Copy application code
COPY app/ ./app/
COPY scripts/mock-model.py ./scripts/

# Create directories
RUN mkdir -p /app/model_cache /app/uploads /app/logs

# Create non-root user
RUN groupadd -r medgemma && useradd -r -g medgemma medgemma && \
    chown -R medgemma:medgemma /app

USER medgemma

EXPOSE 8000

HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

CMD ["python", "-m", "uvicorn", "app.main_simple:app", "--host", "0.0.0.0", "--port", "8000"]
