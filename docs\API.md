# MedGemma AI Chat - API Documentation

This document describes the REST API endpoints for the MedGemma AI Chat application.

## 🔐 Authentication

All API endpoints require authentication using a Bearer token in the Authorization header.

```bash
Authorization: Bearer your-api-key
```

## 📋 Base URL

```
https://your-domain.com/api
```

## 🔍 Health Check

### GET /health

Check the health status of the API service.

**Request:**
```bash
curl -X GET https://your-domain.com/api/health
```

**Response:**
```json
{
  "status": "healthy",
  "model_loaded": true,
  "version": "1.0.0",
  "timestamp": "2024-01-01T12:00:00Z",
  "uptime": 3600.5
}
```

**Status Codes:**
- `200` - Service is healthy
- `503` - Service is unhealthy

## 💬 Chat Endpoints

### POST /chat

Send a text message to the AI and receive a response.

**Request:**
```bash
curl -X POST https://your-domain.com/api/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "message": "What are the symptoms of diabetes?",
    "conversation_id": "conv_123456789",
    "max_length": 2048,
    "temperature": 0.7,
    "top_p": 0.9
  }'
```

**Request Body:**
```json
{
  "message": "string (required, 1-4000 chars)",
  "conversation_id": "string (optional)",
  "max_length": "integer (optional, 1-4096, default: 2048)",
  "temperature": "float (optional, 0.0-2.0, default: 0.7)",
  "top_p": "float (optional, 0.0-1.0, default: 0.9)",
  "system_prompt": "string (optional, max 1000 chars)"
}
```

**Response:**
```json
{
  "response": "Diabetes symptoms include increased thirst, frequent urination, extreme fatigue, blurred vision, and slow-healing cuts or bruises...",
  "conversation_id": "conv_123456789",
  "model": "medgemma-4b-it",
  "timestamp": "2024-01-01T12:00:00Z",
  "usage": {
    "prompt_tokens": 25,
    "completion_tokens": 150,
    "total_tokens": 175
  }
}
```

**Status Codes:**
- `200` - Success
- `400` - Invalid request
- `401` - Unauthorized
- `429` - Rate limit exceeded
- `500` - Server error

### POST /chat/stream

Send a text message and receive a streaming response.

**Request:**
```bash
curl -X POST https://your-domain.com/api/chat/stream \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "message": "Explain how the heart works",
    "conversation_id": "conv_123456789"
  }'
```

**Response (Server-Sent Events):**
```
data: {"chunk": "The", "conversation_id": "conv_123456789"}

data: {"chunk": " heart", "conversation_id": "conv_123456789"}

data: {"chunk": " is", "conversation_id": "conv_123456789"}

...

data: [DONE]
```

**Headers:**
- `Content-Type: text/plain`
- `Cache-Control: no-cache`
- `Connection: keep-alive`

## 🖼️ Image Analysis

### POST /analyze-image

Upload an image with a text prompt for AI analysis.

**Request:**
```bash
curl -X POST https://your-domain.com/api/analyze-image \
  -H "Authorization: Bearer your-api-key" \
  -F "message=Analyze this chest X-ray" \
  -F "image=@chest-xray.jpg" \
  -F "conversation_id=conv_123456789" \
  -F "max_length=2048" \
  -F "temperature=0.7"
```

**Form Data:**
- `message` (required): Text prompt for image analysis
- `image` (required): Image file (JPEG, PNG, GIF, BMP, TIFF)
- `conversation_id` (optional): Conversation ID for context
- `max_length` (optional): Maximum response length (default: 2048)
- `temperature` (optional): Generation temperature (default: 0.7)
- `top_p` (optional): Top-p sampling (default: 0.9)

**File Constraints:**
- Maximum file size: 10MB
- Supported formats: JPEG, PNG, GIF, BMP, TIFF
- Recommended resolution: 896x896 pixels

**Response:**
```json
{
  "response": "This chest X-ray shows clear lung fields with no obvious abnormalities. The heart size appears normal, and the diaphragm is well-defined...",
  "conversation_id": "conv_123456789",
  "model": "medgemma-4b-it",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**Status Codes:**
- `200` - Success
- `400` - Invalid file or request
- `413` - File too large
- `415` - Unsupported file type

## 📚 Conversation Management

### GET /conversations/{conversation_id}

Retrieve conversation history.

**Request:**
```bash
curl -X GET https://your-domain.com/api/conversations/conv_123456789 \
  -H "Authorization: Bearer your-api-key"
```

**Response:**
```json
{
  "conversation_id": "conv_123456789",
  "history": [
    {
      "role": "user",
      "content": "What are the symptoms of diabetes?",
      "timestamp": "2024-01-01T12:00:00Z"
    },
    {
      "role": "assistant",
      "content": "Diabetes symptoms include...",
      "timestamp": "2024-01-01T12:00:05Z"
    }
  ]
}
```

### DELETE /conversations/{conversation_id}

Delete conversation history.

**Request:**
```bash
curl -X DELETE https://your-domain.com/api/conversations/conv_123456789 \
  -H "Authorization: Bearer your-api-key"
```

**Response:**
```json
{
  "message": "Conversation deleted successfully"
}
```

## 📊 Monitoring

### GET /metrics

Get Prometheus metrics (restricted access).

**Request:**
```bash
curl -X GET https://your-domain.com/api/metrics \
  -H "Authorization: Bearer your-api-key"
```

**Response:**
```
# HELP medgemma_requests_total Total requests
# TYPE medgemma_requests_total counter
medgemma_requests_total{method="POST",endpoint="/chat"} 150

# HELP medgemma_request_duration_seconds Request duration
# TYPE medgemma_request_duration_seconds histogram
medgemma_request_duration_seconds_bucket{le="0.1"} 10
...
```

## ❌ Error Responses

All endpoints return standardized error responses:

```json
{
  "error": "Error message",
  "error_code": "ERROR_CODE",
  "details": {
    "field": "additional_info"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Common Error Codes

- `AUTHENTICATION_FAILED` - Invalid or missing API key
- `VALIDATION_ERROR` - Invalid request parameters
- `MODEL_NOT_LOADED` - AI model not ready
- `MODEL_INFERENCE_ERROR` - Error during AI processing
- `FILE_TOO_LARGE` - Uploaded file exceeds size limit
- `UNSUPPORTED_FILE_TYPE` - Invalid file format
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `CONVERSATION_NOT_FOUND` - Invalid conversation ID
- `SERVICE_UNAVAILABLE` - Service temporarily down

## 🔄 Rate Limiting

API endpoints are rate-limited to prevent abuse:

- **Default**: 100 requests per minute
- **Image uploads**: 2 requests per minute
- **Streaming**: 10 concurrent connections

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

## 📝 Request/Response Examples

### Complete Chat Session

```bash
# 1. Start conversation
curl -X POST https://your-domain.com/api/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "message": "Hello, I have questions about heart health"
  }'

# Response includes conversation_id: "conv_abc123"

# 2. Continue conversation
curl -X POST https://your-domain.com/api/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "message": "What foods are good for heart health?",
    "conversation_id": "conv_abc123"
  }'

# 3. Upload medical image
curl -X POST https://your-domain.com/api/analyze-image \
  -H "Authorization: Bearer your-api-key" \
  -F "message=Please analyze this ECG" \
  -F "image=@ecg-reading.jpg" \
  -F "conversation_id=conv_abc123"

# 4. Get conversation history
curl -X GET https://your-domain.com/api/conversations/conv_abc123 \
  -H "Authorization: Bearer your-api-key"
```

### Error Handling

```javascript
// JavaScript example with error handling
async function sendMessage(message) {
  try {
    const response = await fetch('/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({ message })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error);
    }

    return await response.json();
  } catch (error) {
    console.error('API Error:', error.message);
    throw error;
  }
}
```

## 🔧 SDK and Libraries

### Python SDK Example

```python
import requests

class MedGemmaClient:
    def __init__(self, api_key, base_url):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {'Authorization': f'Bearer {api_key}'}
    
    def chat(self, message, conversation_id=None):
        data = {'message': message}
        if conversation_id:
            data['conversation_id'] = conversation_id
        
        response = requests.post(
            f'{self.base_url}/chat',
            json=data,
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()
    
    def analyze_image(self, message, image_path, conversation_id=None):
        files = {'image': open(image_path, 'rb')}
        data = {'message': message}
        if conversation_id:
            data['conversation_id'] = conversation_id
        
        response = requests.post(
            f'{self.base_url}/analyze-image',
            files=files,
            data=data,
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()

# Usage
client = MedGemmaClient('your-api-key', 'https://your-domain.com/api')
result = client.chat('What are the symptoms of diabetes?')
print(result['response'])
```

## 📚 Additional Resources

- [Deployment Guide](DEPLOYMENT.md)
- [Configuration Guide](CONFIGURATION.md)
- [Troubleshooting Guide](TROUBLESHOOTING.md)
- [OpenAPI Specification](openapi.yaml) (if available)
