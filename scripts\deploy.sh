#!/bin/bash

# MedGemma AI Chat Deployment Script
# This script automates the deployment process on AWS EC2 Ubuntu 24.04 LTS

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   error "This script should not be run as root. Please run as ubuntu user."
fi

# Configuration
DOMAIN_NAME=""
SSL_EMAIL=""
API_KEY=""
HUGGINGFACE_TOKEN=""
REDIS_PASSWORD=""

# Function to generate secure password
generate_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-32
}

# Function to validate domain name
validate_domain() {
    if [[ ! $1 =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; then
        return 1
    fi
    return 0
}

# Function to validate email
validate_email() {
    if [[ ! $1 =~ ^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$ ]]; then
        return 1
    fi
    return 0
}

# Function to collect configuration
collect_config() {
    log "Collecting deployment configuration..."
    
    # Domain name
    while true; do
        read -p "Enter your domain name (e.g., example.com): " DOMAIN_NAME
        if validate_domain "$DOMAIN_NAME"; then
            break
        else
            warn "Invalid domain name format. Please try again."
        fi
    done
    
    # SSL email
    while true; do
        read -p "Enter email for SSL certificate (for Let's Encrypt): " SSL_EMAIL
        if validate_email "$SSL_EMAIL"; then
            break
        else
            warn "Invalid email format. Please try again."
        fi
    done
    
    # API key
    read -p "Enter API key (leave empty to generate): " API_KEY
    if [[ -z "$API_KEY" ]]; then
        API_KEY=$(generate_password)
        log "Generated API key: $API_KEY"
    fi
    
    # HuggingFace token
    while true; do
        read -p "Enter HuggingFace token (required for MedGemma): " HUGGINGFACE_TOKEN
        if [[ -n "$HUGGINGFACE_TOKEN" ]]; then
            break
        else
            warn "HuggingFace token is required. Get it from https://huggingface.co/settings/tokens"
        fi
    done
    
    # Redis password
    read -p "Enter Redis password (leave empty to generate): " REDIS_PASSWORD
    if [[ -z "$REDIS_PASSWORD" ]]; then
        REDIS_PASSWORD=$(generate_password)
        log "Generated Redis password: $REDIS_PASSWORD"
    fi
    
    log "Configuration collected successfully!"
}

# Function to check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check OS
    if [[ ! -f /etc/os-release ]] || ! grep -q "Ubuntu 24.04" /etc/os-release; then
        warn "This script is designed for Ubuntu 24.04 LTS"
    fi
    
    # Check memory
    MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    if [[ $MEMORY_GB -lt 15 ]]; then
        warn "Recommended memory is 16GB, you have ${MEMORY_GB}GB"
    fi
    
    # Check disk space
    DISK_GB=$(df -BG / | awk 'NR==2{print $4}' | sed 's/G//')
    if [[ $DISK_GB -lt 40 ]]; then
        warn "Recommended free disk space is 50GB, you have ${DISK_GB}GB"
    fi
    
    log "System requirements check completed"
}

# Function to update system
update_system() {
    log "Updating system packages..."
    sudo apt update
    sudo apt upgrade -y
    sudo apt install -y curl wget git unzip software-properties-common
    log "System updated successfully"
}

# Function to install Docker
install_docker() {
    log "Installing Docker..."
    
    if command -v docker &> /dev/null; then
        log "Docker is already installed"
        return
    fi
    
    # Install Docker
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    rm get-docker.sh
    
    # Add user to docker group
    sudo usermod -aG docker $USER
    
    # Start and enable Docker
    sudo systemctl start docker
    sudo systemctl enable docker
    
    log "Docker installed successfully"
}

# Function to install Docker Compose
install_docker_compose() {
    log "Installing Docker Compose..."
    
    if command -v docker-compose &> /dev/null; then
        log "Docker Compose is already installed"
        return
    fi
    
    # Install Docker Compose
    sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    
    # Verify installation
    docker-compose --version
    
    log "Docker Compose installed successfully"
}

# Function to setup firewall
setup_firewall() {
    log "Setting up firewall..."
    
    # Install and configure UFW
    sudo apt install -y ufw
    sudo ufw --force reset
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    sudo ufw allow ssh
    sudo ufw allow 80
    sudo ufw allow 443
    sudo ufw --force enable
    
    log "Firewall configured successfully"
}

# Function to create environment file
create_env_file() {
    log "Creating environment configuration..."
    
    cat > .env << EOF
# MedGemma AI Chat Configuration
# Generated on $(date)

# API Configuration
API_KEY=$API_KEY
HOST=0.0.0.0
PORT=8000
DEBUG=false
ENVIRONMENT=production

# Model Configuration
MODEL_NAME=google/medgemma-4b-it
MODEL_CACHE_DIR=/app/model_cache
MAX_LENGTH=2048
TEMPERATURE=0.7
TOP_P=0.9

# HuggingFace Configuration
HUGGINGFACE_TOKEN=$HUGGINGFACE_TOKEN
HF_TOKEN=$HUGGINGFACE_TOKEN

# Performance Configuration
WORKERS=1
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=300

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_DIR=/app/uploads
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif,image/bmp,image/tiff

# Security Configuration
CORS_ORIGINS=https://$DOMAIN_NAME,https://www.$DOMAIN_NAME
ALLOWED_HOSTS=$DOMAIN_NAME,www.$DOMAIN_NAME,localhost

# Database Configuration
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=$REDIS_PASSWORD
CONVERSATION_HISTORY_LIMIT=50
CONVERSATION_TTL=86400

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_DIR=/app/logs

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090

# Rate Limiting Configuration
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# SSL Configuration
DOMAIN_NAME=$DOMAIN_NAME
SSL_EMAIL=$SSL_EMAIL
SSL_CERT_PATH=/etc/nginx/ssl/fullchain.pem
SSL_KEY_PATH=/etc/nginx/ssl/privkey.pem

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
EOF
    
    log "Environment file created successfully"
}

# Function to build and start services
deploy_services() {
    log "Building and deploying services..."
    
    # Build services
    docker-compose build
    
    # Start services
    docker-compose up -d
    
    log "Services deployed successfully"
}

# Function to setup SSL certificate
setup_ssl() {
    log "Setting up SSL certificate..."
    
    # Wait for nginx to start
    sleep 10
    
    # Create webroot directory
    sudo mkdir -p /var/www/certbot
    
    # Generate Let's Encrypt certificate
    docker-compose run --rm certbot certonly --webroot \
        --webroot-path=/var/www/certbot \
        --email $SSL_EMAIL \
        --agree-tos \
        --no-eff-email \
        -d $DOMAIN_NAME \
        -d www.$DOMAIN_NAME
    
    # Restart nginx with SSL
    docker-compose restart nginx
    
    log "SSL certificate configured successfully"
}

# Function to verify deployment
verify_deployment() {
    log "Verifying deployment..."
    
    # Wait for services to start
    sleep 30
    
    # Check service status
    docker-compose ps
    
    # Test health endpoint
    if curl -f -k https://$DOMAIN_NAME/api/health; then
        log "Health check passed"
    else
        warn "Health check failed - services may still be starting"
    fi
    
    log "Deployment verification completed"
}

# Function to display completion message
show_completion() {
    log "Deployment completed successfully!"
    echo
    echo -e "${BLUE}=== Deployment Summary ===${NC}"
    echo -e "Domain: https://$DOMAIN_NAME"
    echo -e "API Key: $API_KEY"
    echo -e "Redis Password: $REDIS_PASSWORD"
    echo
    echo -e "${BLUE}=== Next Steps ===${NC}"
    echo "1. Test the application at https://$DOMAIN_NAME"
    echo "2. Save your API key and Redis password securely"
    echo "3. Set up monitoring and backups"
    echo "4. Review logs: docker-compose logs -f"
    echo
    echo -e "${BLUE}=== Important Notes ===${NC}"
    echo "- API key is required for all API requests"
    echo "- SSL certificate will auto-renew via Let's Encrypt"
    echo "- Check troubleshooting guide if you encounter issues"
    echo
}

# Main deployment function
main() {
    log "Starting MedGemma AI Chat deployment..."
    
    # Check if .env already exists
    if [[ -f .env ]]; then
        read -p "Configuration file (.env) already exists. Overwrite? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "Using existing configuration"
        else
            collect_config
            create_env_file
        fi
    else
        collect_config
        create_env_file
    fi
    
    check_requirements
    update_system
    install_docker
    install_docker_compose
    setup_firewall
    deploy_services
    setup_ssl
    verify_deployment
    show_completion
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
