version: '3.8'

services:
  medgemma-api-test:
    build:
      context: .
      dockerfile: docker/Dockerfile.test
    container_name: medgemma-api-test
    restart: unless-stopped
    environment:
      - USE_MOCK_MODEL=true
      - ENVIRONMENT=test
      - API_KEY=test-api-key-for-validation
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - REDIS_URL=redis://redis-test:6379
      - REDIS_PASSWORD=test-redis-password
      - CORS_ORIGINS=*
      - ALLOWED_HOSTS=*
    ports:
      - "8001:8000"
    volumes:
      - test_uploads:/app/uploads
      - test_logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - test-network
    depends_on:
      redis-test:
        condition: service_healthy

  nginx-test:
    build:
      context: .
      dockerfile: docker/Dockerfile.nginx.dev
    container_name: nginx-test
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./nginx/nginx.test.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d/medgemma.test.conf:/etc/nginx/conf.d/default.conf:ro
      - ./frontend:/usr/share/nginx/html:ro
      - test_nginx_logs:/var/log/nginx
    environment:
      - API_UPSTREAM=medgemma-api-test:8000
    networks:
      - test-network
    depends_on:
      medgemma-api-test:
        condition: service_healthy

  redis-test:
    image: redis:7-alpine
    container_name: redis-test
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass test-redis-password
    ports:
      - "6380:6379"
    volumes:
      - test_redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - test-network

  # Test runner service
  test-runner:
    build:
      context: .
      dockerfile: docker/Dockerfile.test
    container_name: test-runner
    environment:
      - ENVIRONMENT=test
      - API_BASE_URL=http://nginx-test
      - TEST_API_KEY=test-api-key-for-validation
    volumes:
      - ./scripts:/app/test-scripts:ro
      - test_results:/app/test-results
    networks:
      - test-network
    depends_on:
      nginx-test:
        condition: service_started
      medgemma-api-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    profiles:
      - testing
    command: >
      sh -c "
        echo 'Waiting for services to be ready...' &&
        sleep 10 &&
        echo 'Running API tests...' &&
        python -c '
import requests
import json
import time

def test_api():
    base_url = \"http://nginx-test\"
    headers = {\"Authorization\": \"Bearer test-api-key-for-validation\"}
    
    # Test health endpoint
    print(\"Testing health endpoint...\")
    response = requests.get(f\"{base_url}/api/health\")
    print(f\"Health: {response.status_code} - {response.json()}\")
    
    # Test chat endpoint
    print(\"Testing chat endpoint...\")
    chat_data = {\"message\": \"Hello, this is a test\"}
    response = requests.post(f\"{base_url}/api/chat\", json=chat_data, headers=headers)
    print(f\"Chat: {response.status_code} - {response.json()}\")
    
    # Test metrics endpoint
    print(\"Testing metrics endpoint...\")
    response = requests.get(f\"{base_url}/api/metrics\")
    print(f\"Metrics: {response.status_code} - {response.json()}\")
    
    print(\"All API tests completed successfully!\")

if __name__ == \"__main__\":
    test_api()
        ' &&
        echo 'Test completed successfully!'
      "

volumes:
  test_redis_data:
    driver: local
  test_uploads:
    driver: local
  test_logs:
    driver: local
  test_nginx_logs:
    driver: local
  test_results:
    driver: local

networks:
  test-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
