version: '3.8'

services:
  medgemma-api:
    build:
      context: .
      dockerfile: docker/Dockerfile.medgemma
    container_name: medgemma-api-dev
    restart: unless-stopped
    environment:
      - MODEL_NAME=${MODEL_NAME:-google/medgemma-4b-it}
      - MAX_LENGTH=${MAX_LENGTH:-2048}
      - TEMPERATURE=${TEMPERATURE:-0.7}
      - TOP_P=${TOP_P:-0.9}
      - API_KEY=${API_KEY:-dev-api-key-for-testing-only}
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}
      - WORKERS=${WORKERS:-1}
      - HOST=0.0.0.0
      - PORT=8000
      - CORS_ORIGINS=*
      - UPLOAD_MAX_SIZE=${UPLOAD_MAX_SIZE:-10485760}
      - CONVERSATION_HISTORY_LIMIT=${CONVERSATION_HISTORY_LIMIT:-50}
      - DEBUG=true
      - ENVIRONMENT=development
      - HUGGINGFACE_TOKEN=${HUGGINGFACE_TOKEN}
      - HF_TOKEN=${HUGGINGFACE_TOKEN}
    volumes:
      - model_cache:/app/model_cache
      - upload_data:/app/uploads
      - logs:/app/logs
      # Mount source code for development (optional - for live reload)
      - ./app:/app/app:ro
    ports:
      - "8000:8000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: 14G
        reservations:
          memory: 12G
    networks:
      - medgemma-network

  nginx-dev:
    build:
      context: .
      dockerfile: docker/Dockerfile.nginx.dev
    container_name: medgemma-nginx-dev
    restart: unless-stopped
    ports:
      - "80:80"
      # No HTTPS port for development
    volumes:
      - ./nginx/nginx.dev.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d/medgemma.dev.conf:/etc/nginx/conf.d/default.conf:ro
      - ./frontend:/usr/share/nginx/html:ro
      - nginx_logs:/var/log/nginx
    environment:
      - API_UPSTREAM=medgemma-api:8000
    depends_on:
      medgemma-api:
        condition: service_healthy
    networks:
      - medgemma-network

  redis:
    image: redis:7-alpine
    container_name: medgemma-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-dev-redis-password}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - medgemma-network

  # Optional: Prometheus for development monitoring
  monitoring:
    image: prom/prometheus:latest
    container_name: medgemma-monitoring-dev
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - medgemma-network

volumes:
  model_cache:
    driver: local
  upload_data:
    driver: local
  logs:
    driver: local
  nginx_logs:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local

networks:
  medgemma-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
