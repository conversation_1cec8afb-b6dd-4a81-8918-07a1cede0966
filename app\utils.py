"""
Utility functions for MedGemma API
"""

import os
import uuid
import hashlib
import asyncio
import logging
import sys
from typing import Optional, Dict, Any, List
from datetime import datetime
from pathlib import Path
import structlog
from structlog.stdlib import LoggerFactory

from .config import settings


def setup_logging():
    """Setup structured logging"""
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if settings.LOG_FORMAT == "json" else structlog.dev.ConsoleRenderer()
        ],
        context_class=dict,
        logger_factory=LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.LOG_LEVEL),
    )
    
    # Create log directory if it doesn't exist
    log_dir = Path(settings.LOG_DIR)
    log_dir.mkdir(parents=True, exist_ok=True)


def create_conversation_id() -> str:
    """Create a unique conversation ID"""
    return str(uuid.uuid4())


def create_request_id() -> str:
    """Create a unique request ID"""
    return str(uuid.uuid4())


def hash_string(text: str) -> str:
    """Create SHA-256 hash of string"""
    return hashlib.sha256(text.encode()).hexdigest()


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe storage"""
    # Remove or replace unsafe characters
    unsafe_chars = '<>:"/\\|?*'
    for char in unsafe_chars:
        filename = filename.replace(char, '_')
    
    # Limit length
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    
    return filename


def get_file_extension(filename: str) -> str:
    """Get file extension"""
    return os.path.splitext(filename)[1].lower()


def is_valid_image_type(content_type: str) -> bool:
    """Check if content type is a valid image type"""
    return content_type in settings.ALLOWED_IMAGE_TYPES


def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def validate_image_file(file_data: bytes, max_size: int = None) -> Dict[str, Any]:
    """Validate image file"""
    max_size = max_size or settings.UPLOAD_MAX_SIZE
    
    # Check file size
    if len(file_data) > max_size:
        return {
            "valid": False,
            "error": f"File size {format_file_size(len(file_data))} exceeds maximum {format_file_size(max_size)}"
        }
    
    # Try to open with PIL to validate it's a real image
    try:
        from PIL import Image
        from io import BytesIO
        
        image = Image.open(BytesIO(file_data))
        image.verify()  # Verify it's a valid image
        
        # Get image info
        image = Image.open(BytesIO(file_data))  # Reopen after verify
        width, height = image.size
        format_name = image.format
        
        return {
            "valid": True,
            "width": width,
            "height": height,
            "format": format_name,
            "size": len(file_data)
        }
        
    except Exception as e:
        return {
            "valid": False,
            "error": f"Invalid image file: {str(e)}"
        }


def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """Truncate text to maximum length"""
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def clean_text(text: str) -> str:
    """Clean and normalize text"""
    # Remove extra whitespace
    text = " ".join(text.split())
    
    # Remove control characters
    text = "".join(char for char in text if ord(char) >= 32 or char in '\n\r\t')
    
    return text.strip()


def extract_keywords(text: str, max_keywords: int = 10) -> List[str]:
    """Extract keywords from text (simple implementation)"""
    # Simple keyword extraction - in production you might use more sophisticated NLP
    import re
    
    # Convert to lowercase and extract words
    words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
    
    # Remove common stop words
    stop_words = {
        'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
        'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
        'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these',
        'those', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have',
        'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
        'may', 'might', 'must', 'can', 'shall'
    }
    
    # Filter out stop words and count frequency
    word_freq = {}
    for word in words:
        if word not in stop_words and len(word) > 2:
            word_freq[word] = word_freq.get(word, 0) + 1
    
    # Sort by frequency and return top keywords
    keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
    return [word for word, freq in keywords[:max_keywords]]


def get_system_info() -> Dict[str, Any]:
    """Get system information"""
    import psutil
    import platform
    
    try:
        # CPU info
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        # Memory info
        memory = psutil.virtual_memory()
        memory_info = {
            "total": memory.total,
            "available": memory.available,
            "percent": memory.percent,
            "used": memory.used,
            "free": memory.free
        }
        
        # Disk info
        disk = psutil.disk_usage('/')
        disk_info = {
            "total": disk.total,
            "used": disk.used,
            "free": disk.free,
            "percent": (disk.used / disk.total) * 100
        }
        
        # System info
        system_info = {
            "platform": platform.platform(),
            "python_version": platform.python_version(),
            "cpu_count": cpu_count,
            "cpu_percent": cpu_percent,
            "memory": memory_info,
            "disk": disk_info
        }
        
        return system_info
        
    except Exception as e:
        return {"error": f"Failed to get system info: {str(e)}"}


def calculate_uptime(start_time: datetime) -> float:
    """Calculate uptime in seconds"""
    return (datetime.utcnow() - start_time).total_seconds()


def format_duration(seconds: float) -> str:
    """Format duration in human readable format"""
    if seconds < 60:
        return f"{seconds:.1f} seconds"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f} minutes"
    elif seconds < 86400:
        hours = seconds / 3600
        return f"{hours:.1f} hours"
    else:
        days = seconds / 86400
        return f"{days:.1f} days"


async def async_retry(
    func,
    max_retries: int = 3,
    delay: float = 1.0,
    backoff: float = 2.0,
    exceptions: tuple = (Exception,)
):
    """Async retry decorator"""
    for attempt in range(max_retries + 1):
        try:
            if asyncio.iscoroutinefunction(func):
                return await func()
            else:
                return func()
        except exceptions as e:
            if attempt == max_retries:
                raise e
            
            wait_time = delay * (backoff ** attempt)
            await asyncio.sleep(wait_time)


def validate_conversation_id(conversation_id: str) -> bool:
    """Validate conversation ID format"""
    try:
        uuid.UUID(conversation_id)
        return True
    except ValueError:
        return False


def create_error_response(error: str, error_code: str = None, details: Dict = None) -> Dict[str, Any]:
    """Create standardized error response"""
    response = {
        "error": error,
        "timestamp": datetime.utcnow().isoformat()
    }
    
    if error_code:
        response["error_code"] = error_code
    
    if details:
        response["details"] = details
    
    return response


def mask_sensitive_info(data: Dict[str, Any], sensitive_keys: List[str] = None) -> Dict[str, Any]:
    """Mask sensitive information in data"""
    if sensitive_keys is None:
        sensitive_keys = ['password', 'api_key', 'token', 'secret', 'key']
    
    masked_data = data.copy()
    
    for key, value in masked_data.items():
        if any(sensitive_key in key.lower() for sensitive_key in sensitive_keys):
            if isinstance(value, str) and len(value) > 4:
                masked_data[key] = value[:4] + "*" * (len(value) - 4)
            else:
                masked_data[key] = "***"
    
    return masked_data


class Timer:
    """Simple timer context manager"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = datetime.utcnow()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = datetime.utcnow()
    
    @property
    def elapsed(self) -> float:
        """Get elapsed time in seconds"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return 0.0
