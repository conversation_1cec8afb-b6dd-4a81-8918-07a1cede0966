#!/bin/bash

# Test build script for development deployment
# This script tests the Docker builds before full deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check if we're in the right directory
if [[ ! -f docker-compose.dev.yml ]]; then
    error "docker-compose.dev.yml not found. Please run this script from the project root directory."
fi

log "Testing Docker builds for development deployment..."

# Test individual service builds
log "Testing MedGemma API build..."
if docker-compose -f docker-compose.dev.yml build medgemma-api; then
    log "✅ MedGemma API build successful"
else
    error "❌ MedGemma API build failed"
fi

log "Testing Nginx development build..."
if docker-compose -f docker-compose.dev.yml build nginx-dev; then
    log "✅ Nginx development build successful"
else
    error "❌ Nginx development build failed"
fi

log "Testing Redis build..."
if docker-compose -f docker-compose.dev.yml pull redis; then
    log "✅ Redis image pull successful"
else
    error "❌ Redis image pull failed"
fi

log "Testing Prometheus build..."
if docker-compose -f docker-compose.dev.yml pull monitoring; then
    log "✅ Prometheus image pull successful"
else
    error "❌ Prometheus image pull failed"
fi

# Test full build
log "Testing complete development build..."
if docker-compose -f docker-compose.dev.yml build; then
    log "✅ Complete development build successful"
else
    error "❌ Complete development build failed"
fi

# Verify images exist
log "Verifying built images..."
IMAGES=$(docker images --format "table {{.Repository}}:{{.Tag}}" | grep -E "(medgemma|nginx)")
if [[ -n "$IMAGES" ]]; then
    log "✅ Built images found:"
    echo "$IMAGES"
else
    warn "⚠️ No MedGemma images found"
fi

# Test configuration validation
log "Testing Docker Compose configuration..."
if docker-compose -f docker-compose.dev.yml config >/dev/null 2>&1; then
    log "✅ Docker Compose configuration is valid"
else
    error "❌ Docker Compose configuration is invalid"
fi

# Check environment file
if [[ -f .env ]]; then
    log "✅ Environment file (.env) exists"
    
    # Check for required variables
    if grep -q "HUGGINGFACE_TOKEN=" .env && ! grep -q "HUGGINGFACE_TOKEN=your-huggingface-token-here" .env; then
        log "✅ HuggingFace token is configured"
    else
        warn "⚠️ HuggingFace token needs to be configured in .env"
    fi
else
    warn "⚠️ Environment file (.env) not found. Copy from .env.development"
fi

log "🎉 Build test completed successfully!"
echo
echo -e "${BLUE}=== Next Steps ===${NC}"
echo "1. If all builds passed, you can now run the full deployment:"
echo "   ./scripts/deploy-dev.sh"
echo
echo "2. Or start services manually:"
echo "   docker-compose -f docker-compose.dev.yml up -d"
echo
echo "3. Check your application URL:"
echo "   ./scripts/get-app-url.sh"
echo
