@echo off
REM MedGemma AI Chat - Windows Testing Script
REM Simple batch script for Windows users who prefer cmd over PowerShell

setlocal enabledelayedexpansion

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║        MedGemma AI Chat - Windows Compatibility Test        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if Docker is installed and running
echo [INFO] Checking Docker installation...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not installed or not in PATH
    echo Please install Docker Desktop and ensure it's running
    pause
    exit /b 1
)

echo [OK] Docker is installed
docker --version

REM Check if Docker is running
echo [INFO] Checking if Docker daemon is running...
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker daemon is not running
    echo Please start Docker Desktop
    pause
    exit /b 1
)

echo [OK] Docker daemon is running

REM Check if we're in Linux containers mode
echo [INFO] Checking container mode...
docker info | findstr "OSType: linux" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Docker may not be in Linux containers mode
    echo Please ensure Docker Desktop is set to Linux containers
)

REM Check project structure
echo [INFO] Checking project structure...
if not exist "docker-compose.test.yml" (
    echo [ERROR] docker-compose.test.yml not found
    echo Please ensure you're in the project root directory
    pause
    exit /b 1
)

if not exist "requirements-test.txt" (
    echo [ERROR] requirements-test.txt not found
    echo Please ensure all project files are present
    pause
    exit /b 1
)

echo [OK] Project structure looks good

REM Clean up any previous test containers
echo [INFO] Cleaning up previous test containers...
docker-compose -f docker-compose.test.yml down -v >nul 2>&1

REM Test 1: Build test containers
echo.
echo [TEST 1] Building test containers...
docker-compose -f docker-compose.test.yml build
if errorlevel 1 (
    echo [ERROR] Failed to build test containers
    echo Check the error messages above
    pause
    exit /b 1
)

echo [OK] Test containers built successfully

REM Test 2: Start services
echo.
echo [TEST 2] Starting test services...
docker-compose -f docker-compose.test.yml up -d
if errorlevel 1 (
    echo [ERROR] Failed to start test services
    echo Check the error messages above
    goto cleanup
)

echo [OK] Test services started

REM Wait for services to be ready
echo [INFO] Waiting for services to initialize...
timeout /t 15 /nobreak >nul

REM Test 3: Check service health
echo.
echo [TEST 3] Checking service health...

REM Check if containers are running
docker-compose -f docker-compose.test.yml ps
echo.

REM Test API health endpoint
echo [INFO] Testing API health endpoint...
curl -f http://localhost:8080/api/health >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Health endpoint not responding
    echo Services may still be starting up
) else (
    echo [OK] Health endpoint is responding
)

REM Test 4: Basic API functionality
echo.
echo [TEST 4] Testing basic API functionality...

REM Test chat endpoint
echo [INFO] Testing chat endpoint...
curl -X POST http://localhost:8080/api/chat ^
     -H "Content-Type: application/json" ^
     -H "Authorization: Bearer test-api-key-for-validation" ^
     -d "{\"message\": \"Hello, this is a test from Windows\"}" >nul 2>&1

if errorlevel 1 (
    echo [WARNING] Chat endpoint test failed
) else (
    echo [OK] Chat endpoint is working
)

REM Test metrics endpoint
echo [INFO] Testing metrics endpoint...
curl -f http://localhost:8080/api/metrics >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Metrics endpoint test failed
) else (
    echo [OK] Metrics endpoint is working
)

REM Test 5: Frontend accessibility
echo.
echo [TEST 5] Testing frontend accessibility...
curl -f http://localhost:8080/ >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Frontend not accessible
) else (
    echo [OK] Frontend is accessible
)

REM Show test results
echo.
echo ═══════════════════════════════════════════════════════════════
echo                        TEST RESULTS
echo ═══════════════════════════════════════════════════════════════
echo.
echo ✅ Docker installation and setup
echo ✅ Project structure validation
echo ✅ Container build process
echo ✅ Service startup
echo.
echo Your application is accessible at:
echo   🌐 Frontend: http://localhost:8080
echo   🔧 API: http://localhost:8080/api
echo   ❤️  Health: http://localhost:8080/api/health
echo.
echo [INFO] Test containers are still running for manual testing
echo [INFO] Run 'docker-compose -f docker-compose.test.yml down' to stop them
echo.

REM Ask user if they want to open the browser
set /p "openBrowser=Open browser to test the application? (y/n): "
if /i "!openBrowser!"=="y" (
    start http://localhost:8080
)

REM Ask user if they want to keep containers running
set /p "keepRunning=Keep test containers running for manual testing? (y/n): "
if /i "!keepRunning!"=="n" (
    goto cleanup
)

echo.
echo [INFO] Test containers are still running
echo [INFO] You can manually test the application at http://localhost:8080
echo [INFO] Run this script again or use 'docker-compose -f docker-compose.test.yml down' to stop
goto end

:cleanup
echo.
echo [INFO] Cleaning up test containers...
docker-compose -f docker-compose.test.yml down -v
echo [OK] Cleanup completed

:end
echo.
echo [INFO] Windows compatibility test completed!
echo.
echo Next steps:
echo 1. If tests passed, your Windows environment is ready
echo 2. Push code to Ubuntu 24.04 server
echo 3. Run './scripts/deploy-dev.sh' on the server
echo 4. The application should work identically on Ubuntu
echo.
pause
