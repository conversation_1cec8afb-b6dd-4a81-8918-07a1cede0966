#!/bin/bash

# Fix script for common test issues
# Run this to resolve the nginx config and build issues

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== Fixing Test Issues ===${NC}"
echo

# Fix 1: Nginx configuration
echo -e "${BLUE}1. Fixing nginx configuration...${NC}"

# Check if the problematic line exists and comment it out
if grep -q "include /usr/share/nginx/modules/\*\.conf;" nginx/nginx.dev.conf; then
    sed -i 's|include /usr/share/nginx/modules/\*\.conf;|# include /usr/share/nginx/modules/\*\.conf;|' nginx/nginx.dev.conf
    echo -e "${GREEN}✅ Fixed nginx.dev.conf${NC}"
else
    echo -e "${GREEN}✅ nginx.dev.conf already fixed${NC}"
fi

if grep -q "include /usr/share/nginx/modules/\*\.conf;" nginx/nginx.conf; then
    sed -i 's|include /usr/share/nginx/modules/\*\.conf;|# include /usr/share/nginx/modules/\*\.conf;|' nginx/nginx.conf
    echo -e "${GREEN}✅ Fixed nginx.conf${NC}"
else
    echo -e "${GREEN}✅ nginx.conf already fixed${NC}"
fi

# Test nginx config
echo -e "${BLUE}Testing nginx configuration...${NC}"
nginx_test_output=$(docker run --rm -v "$(pwd)/nginx/nginx.dev.conf:/etc/nginx/nginx.conf:ro" nginx:alpine nginx -t 2>&1)
if echo "$nginx_test_output" | grep -q "syntax is ok" && echo "$nginx_test_output" | grep -q "test is successful"; then
    echo -e "${GREEN}✅ Nginx configuration is now valid${NC}"
elif echo "$nginx_test_output" | grep -q "Configuration complete"; then
    echo -e "${GREEN}✅ Nginx configuration is valid (startup successful)${NC}"
else
    echo -e "${RED}❌ Nginx configuration still has issues${NC}"
    echo "Test output: $nginx_test_output"
fi

# Fix 2: Create missing test files
echo -e "${BLUE}2. Creating missing test files...${NC}"

# Ensure scripts directory exists
mkdir -p scripts

# Make sure mock-model.py is executable
if [[ -f scripts/mock-model.py ]]; then
    chmod +x scripts/mock-model.py
    echo -e "${GREEN}✅ mock-model.py is executable${NC}"
else
    echo -e "${YELLOW}⚠️  mock-model.py not found${NC}"
fi

# Fix 3: Clean Docker environment
echo -e "${BLUE}3. Cleaning Docker environment...${NC}"
docker system prune -f >/dev/null 2>&1 || true
echo -e "${GREEN}✅ Docker environment cleaned${NC}"

# Fix 4: Verify environment file
echo -e "${BLUE}4. Checking environment file...${NC}"
if [[ ! -f .env ]] && [[ -f .env.development ]]; then
    cp .env.development .env
    echo -e "${GREEN}✅ Created .env from .env.development${NC}"
elif [[ -f .env ]]; then
    echo -e "${GREEN}✅ .env file exists${NC}"
else
    echo -e "${YELLOW}⚠️  No environment file found${NC}"
fi

# Fix 5: Test basic Docker functionality
echo -e "${BLUE}5. Testing Docker functionality...${NC}"
if docker run --rm hello-world >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Docker is working correctly${NC}"
else
    echo -e "${RED}❌ Docker has issues${NC}"
    echo "You may need to restart Docker or check permissions"
fi

echo
echo -e "${GREEN}🔧 Fix script completed!${NC}"
echo
echo -e "${BLUE}Next steps:${NC}"
echo "1. Run: ./scripts/validate-config.sh"
echo "2. If validation passes, run: ./scripts/run-tests.sh quick"
echo "3. If tests pass, run: ./scripts/deploy-dev.sh"
echo
