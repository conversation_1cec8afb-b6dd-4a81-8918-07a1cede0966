"""
Conversation Manager for handling chat history
"""

import json
import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import redis.asyncio as redis
import structlog

from .config import settings
from .models import Message, ConversationHistory
from .exceptions import MedGemmaException

logger = structlog.get_logger()


class ConversationManager:
    """Manages conversation history using Redis"""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self._initialized = False
    
    async def initialize(self):
        """Initialize Redis connection"""
        if self._initialized:
            return
        
        try:
            # Parse Redis URL
            redis_url = settings.REDIS_URL
            if settings.REDIS_PASSWORD:
                # Add password to URL if not already present
                if "@" not in redis_url:
                    redis_url = redis_url.replace("://", f"://:{settings.REDIS_PASSWORD}@")
            
            self.redis_client = redis.from_url(
                redis_url,
                encoding="utf-8",
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # Test connection
            await self.redis_client.ping()
            logger.info("Redis connection established successfully")
            self._initialized = True
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis connection: {e}")
            # Fallback to in-memory storage
            self.redis_client = None
            self._conversations: Dict[str, List[Message]] = {}
            logger.warning("Using in-memory conversation storage as fallback")
            self._initialized = True
    
    async def add_message(self, conversation_id: str, message: Message):
        """Add a message to conversation history"""
        if not self._initialized:
            await self.initialize()
        
        try:
            if self.redis_client:
                await self._add_message_redis(conversation_id, message)
            else:
                await self._add_message_memory(conversation_id, message)
        except Exception as e:
            logger.error(f"Failed to add message to conversation {conversation_id}: {e}")
            raise MedGemmaException(f"Failed to save message: {str(e)}")
    
    async def get_conversation(self, conversation_id: str) -> List[Message]:
        """Get conversation history"""
        if not self._initialized:
            await self.initialize()
        
        try:
            if self.redis_client:
                return await self._get_conversation_redis(conversation_id)
            else:
                return await self._get_conversation_memory(conversation_id)
        except Exception as e:
            logger.error(f"Failed to get conversation {conversation_id}: {e}")
            return []
    
    async def delete_conversation(self, conversation_id: str):
        """Delete conversation history"""
        if not self._initialized:
            await self.initialize()
        
        try:
            if self.redis_client:
                await self._delete_conversation_redis(conversation_id)
            else:
                await self._delete_conversation_memory(conversation_id)
        except Exception as e:
            logger.error(f"Failed to delete conversation {conversation_id}: {e}")
            raise MedGemmaException(f"Failed to delete conversation: {str(e)}")
    
    async def get_conversation_list(self, limit: int = 100) -> List[str]:
        """Get list of conversation IDs"""
        if not self._initialized:
            await self.initialize()
        
        try:
            if self.redis_client:
                return await self._get_conversation_list_redis(limit)
            else:
                return await self._get_conversation_list_memory(limit)
        except Exception as e:
            logger.error(f"Failed to get conversation list: {e}")
            return []
    
    async def cleanup_expired_conversations(self):
        """Clean up expired conversations"""
        if not self._initialized:
            await self.initialize()
        
        try:
            if self.redis_client:
                await self._cleanup_expired_redis()
            else:
                await self._cleanup_expired_memory()
        except Exception as e:
            logger.error(f"Failed to cleanup expired conversations: {e}")
    
    # Redis implementation methods
    async def _add_message_redis(self, conversation_id: str, message: Message):
        """Add message using Redis"""
        key = f"conversation:{conversation_id}"
        
        # Serialize message
        message_data = {
            "role": message.role.value,
            "content": message.content if isinstance(message.content, str) else [
                {"type": c.type.value, "text": c.text, "image_url": c.image_url}
                for c in message.content
            ],
            "timestamp": message.timestamp.isoformat() if message.timestamp else datetime.utcnow().isoformat()
        }
        
        # Add to list
        await self.redis_client.lpush(key, json.dumps(message_data))
        
        # Trim to max length
        await self.redis_client.ltrim(key, 0, settings.CONVERSATION_HISTORY_LIMIT - 1)
        
        # Set expiration
        await self.redis_client.expire(key, settings.CONVERSATION_TTL)
        
        # Update metadata
        metadata_key = f"conversation_meta:{conversation_id}"
        await self.redis_client.hset(metadata_key, {
            "updated_at": datetime.utcnow().isoformat(),
            "message_count": await self.redis_client.llen(key)
        })
        await self.redis_client.expire(metadata_key, settings.CONVERSATION_TTL)
    
    async def _get_conversation_redis(self, conversation_id: str) -> List[Message]:
        """Get conversation using Redis"""
        key = f"conversation:{conversation_id}"
        
        # Get messages (reversed to maintain chronological order)
        messages_data = await self.redis_client.lrange(key, 0, -1)
        messages_data.reverse()
        
        messages = []
        for msg_json in messages_data:
            try:
                msg_data = json.loads(msg_json)
                
                # Parse content
                content = msg_data["content"]
                if isinstance(content, list):
                    from .models import MessageContent, ContentType
                    content = [
                        MessageContent(
                            type=ContentType(c["type"]),
                            text=c.get("text"),
                            image_url=c.get("image_url")
                        )
                        for c in content
                    ]
                
                message = Message(
                    role=msg_data["role"],
                    content=content,
                    timestamp=datetime.fromisoformat(msg_data["timestamp"])
                )
                messages.append(message)
            except Exception as e:
                logger.warning(f"Failed to parse message in conversation {conversation_id}: {e}")
                continue
        
        return messages
    
    async def _delete_conversation_redis(self, conversation_id: str):
        """Delete conversation using Redis"""
        key = f"conversation:{conversation_id}"
        metadata_key = f"conversation_meta:{conversation_id}"
        
        await self.redis_client.delete(key, metadata_key)
    
    async def _get_conversation_list_redis(self, limit: int) -> List[str]:
        """Get conversation list using Redis"""
        pattern = "conversation_meta:*"
        conversation_ids = []
        
        async for key in self.redis_client.scan_iter(match=pattern, count=100):
            conversation_id = key.replace("conversation_meta:", "")
            conversation_ids.append(conversation_id)
            
            if len(conversation_ids) >= limit:
                break
        
        return conversation_ids
    
    async def _cleanup_expired_redis(self):
        """Cleanup expired conversations using Redis"""
        # Redis handles TTL automatically, but we can clean up orphaned metadata
        pattern = "conversation_meta:*"
        expired_count = 0
        
        async for key in self.redis_client.scan_iter(match=pattern, count=100):
            conversation_id = key.replace("conversation_meta:", "")
            conversation_key = f"conversation:{conversation_id}"
            
            # Check if conversation exists
            if not await self.redis_client.exists(conversation_key):
                await self.redis_client.delete(key)
                expired_count += 1
        
        if expired_count > 0:
            logger.info(f"Cleaned up {expired_count} orphaned conversation metadata entries")
    
    # In-memory implementation methods (fallback)
    async def _add_message_memory(self, conversation_id: str, message: Message):
        """Add message using in-memory storage"""
        if conversation_id not in self._conversations:
            self._conversations[conversation_id] = []
        
        self._conversations[conversation_id].append(message)
        
        # Trim to max length
        if len(self._conversations[conversation_id]) > settings.CONVERSATION_HISTORY_LIMIT:
            self._conversations[conversation_id] = self._conversations[conversation_id][-settings.CONVERSATION_HISTORY_LIMIT:]
    
    async def _get_conversation_memory(self, conversation_id: str) -> List[Message]:
        """Get conversation using in-memory storage"""
        return self._conversations.get(conversation_id, [])
    
    async def _delete_conversation_memory(self, conversation_id: str):
        """Delete conversation using in-memory storage"""
        if conversation_id in self._conversations:
            del self._conversations[conversation_id]
    
    async def _get_conversation_list_memory(self, limit: int) -> List[str]:
        """Get conversation list using in-memory storage"""
        return list(self._conversations.keys())[:limit]
    
    async def _cleanup_expired_memory(self):
        """Cleanup expired conversations using in-memory storage"""
        # For in-memory storage, we don't have TTL, so this is a no-op
        # In a real implementation, you might track timestamps and clean up old conversations
        pass
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.redis_client:
            await self.redis_client.close()
            logger.info("Redis connection closed")
        
        if hasattr(self, '_conversations'):
            self._conversations.clear()
        
        self._initialized = False
