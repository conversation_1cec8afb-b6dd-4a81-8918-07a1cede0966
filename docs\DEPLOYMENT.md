# MedGemma AI Chat - Production Deployment Guide

This guide provides step-by-step instructions for deploying the MedGemma AI Chat application on AWS EC2 Ubuntu 24.04 LTS.

## 📋 Prerequisites

### AWS Requirements
- AWS EC2 t3.xlarge instance (4 vCPU, 16 GB RAM)
- Ubuntu 24.04 LTS AMI
- At least 50GB EBS storage
- Security group with appropriate ports open
- Elastic IP address (recommended)
- Domain name pointing to your instance

### Local Requirements
- SSH client
- Domain name with DNS access
- HuggingFace account with MedGemma access

## 🚀 Step 1: EC2 Instance Setup

### 1.1 Launch EC2 Instance

1. **Launch Instance:**
   ```bash
   # Instance Type: t3.xlarge
   # AMI: Ubuntu 24.04 LTS
   # Storage: 50GB gp3 SSD
   # Key Pair: Create or use existing
   ```

2. **Configure Security Group:**
   ```bash
   # Inbound Rules:
   # SSH (22) - Your IP
   # HTTP (80) - 0.0.0.0/0
   # HTTPS (443) - 0.0.0.0/0
   # Custom TCP (8000) - Your IP (for testing)
   ```

### 1.2 Connect to Instance

```bash
# Connect via SSH
ssh -i your-key.pem ubuntu@your-instance-ip

# Update system
sudo apt update && sudo apt upgrade -y
```

## 🐳 Step 2: Install Docker and Docker Compose

### 2.1 Install Docker

```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Add user to docker group
sudo usermod -aG docker ubuntu

# Start and enable Docker
sudo systemctl start docker
sudo systemctl enable docker

# Logout and login again for group changes
exit
# SSH back in
```

### 2.2 Install Docker Compose

```bash
# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Verify installation
docker --version
docker-compose --version
```

## 📁 Step 3: Deploy Application

### 3.1 Clone Repository

```bash
# Clone the repository
git clone https://github.com/your-username/docker-medgemma-fastapi.git
cd docker-medgemma-fastapi

# Or upload files manually if not using git
```

### 3.2 Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env
```

**Required Environment Variables:**
```bash
# API Configuration
API_KEY=your-super-secret-api-key-32-chars-min
DOMAIN_NAME=your-domain.com
SSL_EMAIL=<EMAIL>

# HuggingFace Token (required for MedGemma)
HUGGINGFACE_TOKEN=hf_your_token_here

# Redis Password
REDIS_PASSWORD=your-secure-redis-password

# CORS Origins
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com
ALLOWED_HOSTS=your-domain.com,www.your-domain.com
```

### 3.3 Set Up SSL Certificate

#### Option A: Let's Encrypt (Recommended)

```bash
# Create directories
sudo mkdir -p /var/www/certbot

# Start nginx temporarily for certificate generation
docker-compose up -d nginx

# Generate certificate
docker-compose run --rm certbot certonly --webroot \
  --webroot-path=/var/www/certbot \
  --email <EMAIL> \
  --agree-tos \
  --no-eff-email \
  -d your-domain.com \
  -d www.your-domain.com

# Restart nginx with SSL
docker-compose restart nginx
```

#### Option B: Self-Signed Certificate (Development)

```bash
# Generate self-signed certificate
sudo mkdir -p ssl
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout ssl/selfsigned.key \
  -out ssl/selfsigned.crt \
  -subj "/C=US/ST=State/L=City/O=Organization/CN=your-domain.com"
```

## 🔧 Step 4: Build and Deploy

### 4.1 Build Services

```bash
# Build all services
docker-compose build

# Pull required images
docker-compose pull
```

### 4.2 Start Services

```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f medgemma-api
```

### 4.3 Verify Deployment

```bash
# Check health endpoint
curl -k https://your-domain.com/api/health

# Expected response:
# {"status":"healthy","model_loaded":true,"version":"1.0.0"}
```

## 🔍 Step 5: Testing and Verification

### 5.1 Test API Endpoints

```bash
# Test text chat
curl -X POST https://your-domain.com/api/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{"message": "Hello, can you help me with medical questions?"}'

# Test image upload (with actual image file)
curl -X POST https://your-domain.com/api/analyze-image \
  -H "Authorization: Bearer your-api-key" \
  -F "message=Analyze this medical image" \
  -F "image=@/path/to/test-image.jpg"
```

### 5.2 Test Frontend

1. Open browser and navigate to `https://your-domain.com`
2. Verify the chat interface loads
3. Test sending a text message
4. Test uploading an image
5. Check browser console for errors

## 📊 Step 6: Monitoring and Maintenance

### 6.1 Monitor Services

```bash
# View service logs
docker-compose logs -f

# Monitor resource usage
docker stats

# Check service health
docker-compose ps
```

### 6.2 Set Up Log Rotation

```bash
# Create logrotate configuration
sudo nano /etc/logrotate.d/docker-medgemma

# Add configuration:
/var/lib/docker/containers/*/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 0644 root root
}
```

### 6.3 Set Up Automatic Updates

```bash
# Create update script
nano update-medgemma.sh

# Add content:
#!/bin/bash
cd /home/<USER>/docker-medgemma-fastapi
docker-compose pull
docker-compose up -d
docker system prune -f

# Make executable
chmod +x update-medgemma.sh

# Add to crontab for weekly updates
crontab -e
# Add: 0 2 * * 0 /home/<USER>/docker-medgemma-fastapi/update-medgemma.sh
```

## 🔒 Step 7: Security Hardening

### 7.1 Firewall Configuration

```bash
# Install and configure UFW
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
```

### 7.2 Fail2Ban Setup

```bash
# Install Fail2Ban
sudo apt install fail2ban -y

# Configure for SSH protection
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

### 7.3 Regular Security Updates

```bash
# Enable automatic security updates
sudo apt install unattended-upgrades -y
sudo dpkg-reconfigure -plow unattended-upgrades
```

## 🔄 Step 8: Backup and Recovery

### 8.1 Set Up Automated Backups

```bash
# Create backup script
nano backup-medgemma.sh

# Add content:
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Backup environment and configs
tar -czf $BACKUP_DIR/medgemma_config_$DATE.tar.gz \
  .env docker-compose.yml nginx/ frontend/

# Backup Redis data
docker-compose exec redis redis-cli BGSAVE
docker cp medgemma-redis:/data/dump.rdb $BACKUP_DIR/redis_$DATE.rdb

# Cleanup old backups (keep 7 days)
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "*.rdb" -mtime +7 -delete

# Make executable and schedule
chmod +x backup-medgemma.sh
crontab -e
# Add: 0 2 * * * /home/<USER>/docker-medgemma-fastapi/backup-medgemma.sh
```

## 🚨 Troubleshooting

### Common Issues

1. **Model Download Fails:**
   ```bash
   # Check HuggingFace token
   docker-compose logs medgemma-api
   # Ensure you've accepted MedGemma license on HuggingFace
   ```

2. **SSL Certificate Issues:**
   ```bash
   # Check certificate files
   docker-compose exec nginx ls -la /etc/nginx/ssl/
   # Regenerate if needed
   ```

3. **Memory Issues:**
   ```bash
   # Monitor memory usage
   free -h
   # Restart services if needed
   docker-compose restart
   ```

4. **Permission Issues:**
   ```bash
   # Fix Docker permissions
   sudo chown -R ubuntu:ubuntu /home/<USER>/docker-medgemma-fastapi
   ```

For more troubleshooting information, see [TROUBLESHOOTING.md](TROUBLESHOOTING.md).

## 📞 Support

- Check logs: `docker-compose logs -f`
- Review configuration: [CONFIGURATION.md](CONFIGURATION.md)
- API documentation: [API.md](API.md)
- GitHub Issues: [Create an issue](https://github.com/your-username/docker-medgemma-fastapi/issues)

## 🎉 Deployment Complete!

Your MedGemma AI Chat application should now be running at `https://your-domain.com`

**Next Steps:**
1. Test all functionality thoroughly
2. Set up monitoring and alerting
3. Configure regular backups
4. Review security settings
5. Plan for scaling if needed
