"""
Test version of main.py that uses mock MedGemma service
"""

import os
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Depends, UploadFile, File, Form, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
import structlog

from .config import settings
from .models import (
    ChatRequest, ChatResponse, ImageAnalysisRequest, HealthResponse,
    ErrorResponse, ModelInfo, UsageStats
)
from .auth import verify_api_key, check_rate_limit, SecurityHeaders
from .utils import setup_logging, create_conversation_id, Timer
from .exceptions import (
    MedGemmaException, handle_medgemma_exception, handle_http_exception,
    handle_generic_exception, handle_validation_exception
)

# Import mock service for testing
from .medgemma_service_mock import create_medgemma_service

# Setup logging
setup_logging()
logger = structlog.get_logger()

# Global service instance
medgemma_service = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global medgemma_service
    
    logger.info("Starting MedGemma AI Chat application (TEST MODE)")
    
    try:
        # Initialize service (mock or real based on environment)
        medgemma_service = create_medgemma_service()
        await medgemma_service.initialize()
        
        logger.info("Application startup completed successfully")
        yield
        
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        raise
    finally:
        # Cleanup
        if medgemma_service:
            await medgemma_service.cleanup()
        logger.info("Application shutdown completed")

# Create FastAPI app
app = FastAPI(
    title="MedGemma AI Chat API (Test Mode)",
    description="AI-powered medical chat application with image analysis capabilities - Test Version",
    version="1.0.0-test",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS if settings.CORS_ORIGINS != ["*"] else ["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add security headers middleware
@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    response = await call_next(request)
    
    # Add security headers
    security_headers = SecurityHeaders.get_security_headers()
    for header, value in security_headers.items():
        response.headers[header] = value
    
    # Add test mode header
    response.headers["X-Test-Mode"] = "true"
    
    return response

# Exception handlers
app.add_exception_handler(MedGemmaException, handle_medgemma_exception)
app.add_exception_handler(HTTPException, handle_http_exception)
app.add_exception_handler(Exception, handle_generic_exception)

# Dependency to get service
async def get_medgemma_service():
    if not medgemma_service or not medgemma_service.is_ready():
        raise HTTPException(
            status_code=503,
            detail="MedGemma service not ready"
        )
    return medgemma_service

# Health check endpoint
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        model_loaded = medgemma_service and medgemma_service.is_model_loaded()
        
        return HealthResponse(
            status="healthy" if model_loaded else "starting",
            model_loaded=model_loaded,
            version="1.0.0-test",
            memory_usage={"test_mode": True}
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthResponse(
            status="unhealthy",
            model_loaded=False,
            version="1.0.0-test"
        )

# Chat endpoint
@app.post("/chat", response_model=ChatResponse)
async def chat(
    request: ChatRequest,
    service = Depends(get_medgemma_service),
    api_key: str = Depends(verify_api_key)
):
    """Chat with the AI"""
    # Rate limiting
    check_rate_limit(api_key)
    
    try:
        with Timer() as timer:
            # Generate conversation ID if not provided
            conversation_id = request.conversation_id or create_conversation_id()
            
            # Generate response
            response = await service.generate_response(
                message=request.message,
                conversation_id=conversation_id,
                max_length=request.max_length,
                temperature=request.temperature,
                top_p=request.top_p
            )
            
            return ChatResponse(
                response=response,
                conversation_id=conversation_id,
                model="mock-medgemma-test",
                usage={"response_time": timer.elapsed}
            )
            
    except Exception as e:
        logger.error(f"Chat error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Image analysis endpoint
@app.post("/analyze-image", response_model=ChatResponse)
async def analyze_image(
    message: str = Form(...),
    image: UploadFile = File(...),
    conversation_id: str = Form(None),
    max_length: int = Form(2048),
    temperature: float = Form(0.7),
    top_p: float = Form(0.9),
    service = Depends(get_medgemma_service),
    api_key: str = Depends(verify_api_key)
):
    """Analyze medical image"""
    # Rate limiting
    check_rate_limit(api_key)
    
    try:
        # Validate image
        if not image.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="Invalid image format")
        
        if image.size > settings.UPLOAD_MAX_SIZE:
            raise HTTPException(status_code=413, detail="Image too large")
        
        # Read image data
        image_data = await image.read()
        
        with Timer() as timer:
            # Generate conversation ID if not provided
            conv_id = conversation_id or create_conversation_id()
            
            # Analyze image
            response = await service.analyze_image(
                image_data=image_data,
                message=message,
                conversation_id=conv_id,
                max_length=max_length,
                temperature=temperature,
                top_p=top_p
            )
            
            return ChatResponse(
                response=response,
                conversation_id=conv_id,
                model="mock-medgemma-test",
                usage={"response_time": timer.elapsed, "image_size": len(image_data)}
            )
            
    except Exception as e:
        logger.error(f"Image analysis error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Streaming chat endpoint
@app.post("/chat/stream")
async def chat_stream(
    request: ChatRequest,
    service = Depends(get_medgemma_service),
    api_key: str = Depends(verify_api_key)
):
    """Stream chat response"""
    # Rate limiting
    check_rate_limit(api_key)
    
    try:
        # Generate conversation ID if not provided
        conversation_id = request.conversation_id or create_conversation_id()
        
        async def generate():
            try:
                async for chunk in service.generate_response_stream(
                    message=request.message,
                    conversation_id=conversation_id,
                    max_length=request.max_length,
                    temperature=request.temperature,
                    top_p=request.top_p
                ):
                    yield f"data: {chunk}\n\n"
                
                yield "data: [DONE]\n\n"
                
            except Exception as e:
                logger.error(f"Streaming error: {e}")
                yield f"data: {{'error': '{str(e)}'}}\n\n"
        
        return StreamingResponse(
            generate(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"
            }
        )
        
    except Exception as e:
        logger.error(f"Stream setup error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Model info endpoint
@app.get("/model/info", response_model=ModelInfo)
async def get_model_info(
    service = Depends(get_medgemma_service),
    api_key: str = Depends(verify_api_key)
):
    """Get model information"""
    return ModelInfo(
        name="mock-medgemma-test",
        version="1.0.0-test",
        description="Mock MedGemma model for testing",
        capabilities=["text_generation", "image_analysis", "streaming"],
        max_context_length=2048,
        supported_languages=["en"]
    )

# Metrics endpoint (basic)
@app.get("/metrics")
async def get_metrics():
    """Get basic metrics"""
    return {
        "test_mode": True,
        "service_status": "running",
        "model_loaded": medgemma_service and medgemma_service.is_model_loaded(),
        "timestamp": "2024-01-01T00:00:00Z"
    }

# Conversation endpoints
@app.get("/conversations/{conversation_id}")
async def get_conversation(
    conversation_id: str,
    service = Depends(get_medgemma_service),
    api_key: str = Depends(verify_api_key)
):
    """Get conversation history"""
    try:
        history = await service.get_conversation_history(conversation_id)
        return {"conversation_id": conversation_id, "history": history}
    except Exception as e:
        logger.error(f"Error getting conversation: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/conversations/{conversation_id}")
async def delete_conversation(
    conversation_id: str,
    service = Depends(get_medgemma_service),
    api_key: str = Depends(verify_api_key)
):
    """Delete conversation"""
    try:
        await service.delete_conversation(conversation_id)
        return {"message": "Conversation deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting conversation: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "MedGemma AI Chat API - Test Mode",
        "version": "1.0.0-test",
        "docs": "/docs",
        "health": "/health"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main_test:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG
    )
