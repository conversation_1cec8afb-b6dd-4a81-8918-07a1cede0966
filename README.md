# Production-Ready MedGemma AI Chat Application

A comprehensive deployment guide for a production-ready AI chat application using Google's MedGemma-4b-it model, FastAPI backend, and Nginx reverse proxy, designed for AWS EC2 deployment.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   Frontend      │    │   Nginx          │    │   FastAPI Backend   │
│   (HTML/JS)     │◄──►│   Reverse Proxy  │◄──►│   + MedGemma Model  │
│                 │    │   + SSL/TLS      │    │                     │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
```

## 🚀 Features

- **Multimodal AI Chat**: Text and image processing with MedGemma-4b-it
- **Production Ready**: Docker containerization with proper orchestration
- **Secure**: SSL/TLS termination, authentication, and security best practices
- **Scalable**: Optimized for AWS EC2 t3.xlarge instances
- **Monitoring**: Health checks, logging, and error handling

## 📋 Prerequisites

### For Development
- AWS EC2 t3.xlarge instance (4 vCPU, 16 GB RAM)
- Ubuntu 24.04 LTS
- HuggingFace account with MedGemma access

### For Production (Additional)
- Domain name pointing to your server
- Email address for SSL certificate registration

## 🛠️ Quick Start

### 🚀 Development Setup (No Domain Required)

Perfect for testing with dynamic IP addresses:

```bash
# Clone the repository
git clone https://github.com/your-username/docker-medgemma-fastapi.git
cd docker-medgemma-fastapi

# Run the development deployment script
chmod +x scripts/deploy-dev.sh
./scripts/deploy-dev.sh

# Get your application URL
./scripts/get-app-url.sh
```

**Access your app at**: `http://YOUR_EC2_PUBLIC_IP`

For detailed development setup, see the [Development Guide](./docs/DEVELOPMENT.md).

### 🏭 Production Deployment (Domain Required)

For production with SSL and domain name:

```bash
# Clone the repository
git clone https://github.com/your-username/docker-medgemma-fastapi.git
cd docker-medgemma-fastapi

# Run the production deployment script
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

For detailed production setup, see the [Deployment Guide](./docs/DEPLOYMENT.md).

## 📁 Project Structure

```
├── app/                    # FastAPI application
├── docker/                 # Docker configurations
├── nginx/                  # Nginx configuration
├── frontend/               # Web interface
├── docs/                   # Documentation
├── scripts/                # Deployment scripts
└── docker-compose.yml      # Service orchestration
```

## 🔧 Configuration

All configuration is managed through environment variables. See [Configuration Guide](./docs/CONFIGURATION.md) for details.

## 🧪 Testing (Recommended First Step)

Before deploying, validate your setup without downloading the 4GB+ model:

```bash
# Quick validation (2-3 minutes)
./scripts/run-tests.sh quick

# Complete test suite (8-10 minutes)
./scripts/run-tests.sh full
```

This tests all infrastructure, APIs, and configuration using lightweight mocks.

## 📖 Documentation

- [Testing Guide](./docs/TESTING.md) - Validate deployment without model download
- [Development Guide](./docs/DEVELOPMENT.md) - Quick setup with dynamic IPs
- [Production Deployment](./docs/DEPLOYMENT.md) - Full production setup
- [Configuration Guide](./docs/CONFIGURATION.md) - All configuration options
- [API Documentation](./docs/API.md) - Complete API reference
- [Troubleshooting](./docs/TROUBLESHOOTING.md) - Common issues and solutions
- [Deployment Checklist](./docs/DEPLOYMENT_CHECKLIST.md) - Production readiness

## 🔒 Security

This application implements security best practices including:
- SSL/TLS encryption
- API authentication
- Input validation
- Rate limiting
- Security headers

## 📊 Monitoring

Built-in monitoring includes:
- Health check endpoints
- Application logging
- Error tracking
- Performance metrics

## 🤝 Contributing

Please read our contributing guidelines before submitting pull requests.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.