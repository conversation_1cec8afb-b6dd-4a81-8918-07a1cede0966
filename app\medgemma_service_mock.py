"""
Mock MedGemma AI Service for Testing

This module provides a mock implementation of the MedGemma service
that can be used for testing without downloading the actual model.
"""

import asyncio
import json
import os
import sys
import time
from typing import Optional, List, Dict, Any, AsyncGenerator
from io import BytesIO
import structlog

# Add scripts directory to path to import mock model
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'scripts'))

try:
    from mock_model import MockMedGemmaModel
except ImportError:
    # Fallback if mock_model is not available
    MockMedGemmaModel = None

from .config import settings
from .models import Message, MessageRole, ContentType, MessageContent
from .conversation_manager import ConversationManager
from .exceptions import MedGemmaException

logger = structlog.get_logger()


class MockMedGemmaService:
    """Mock service for handling MedGemma model inference during testing"""
    
    def __init__(self):
        self.model = None
        self.conversation_manager = ConversationManager()
        self._model_loaded = False
        self._initialization_lock = asyncio.Lock()
        self._inference_lock = asyncio.Semaphore(settings.MAX_CONCURRENT_REQUESTS)
        
        # Check if we should use mock mode
        self.use_mock = os.getenv('USE_MOCK_MODEL', 'false').lower() == 'true'
        
    async def initialize(self):
        """Initialize the mock MedGemma model"""
        async with self._initialization_lock:
            if self._model_loaded:
                return
                
            logger.info("Initializing Mock MedGemma service...")
            start_time = time.time()
            
            try:
                if self.use_mock and MockMedGemmaModel:
                    # Use mock model for testing
                    self.model = MockMedGemmaModel()
                    await self.model.initialize()
                    logger.info("Mock MedGemma model loaded successfully")
                else:
                    # Fallback to simple mock responses
                    self.model = SimpleMockModel()
                    await self.model.initialize()
                    logger.info("Simple mock model loaded successfully")
                
                # Initialize conversation manager
                await self.conversation_manager.initialize()
                
                self._model_loaded = True
                load_time = time.time() - start_time
                logger.info(f"Mock MedGemma service initialized in {load_time:.2f} seconds")
                
            except Exception as e:
                logger.error(f"Failed to initialize Mock MedGemma service: {e}")
                raise MedGemmaException(f"Mock service initialization failed: {str(e)}")
    
    def is_ready(self) -> bool:
        """Check if the mock service is ready"""
        return self._model_loaded and self.model is not None
    
    def is_model_loaded(self) -> bool:
        """Check if the mock model is loaded"""
        return self._model_loaded
    
    async def generate_response(
        self,
        message: str,
        conversation_id: str,
        max_length: int = None,
        temperature: float = None,
        top_p: float = None
    ) -> str:
        """Generate a mock text response"""
        if not self.is_ready():
            raise MedGemmaException("Mock service not ready")
        
        async with self._inference_lock:
            try:
                # Get conversation history
                history = await self.conversation_manager.get_conversation(conversation_id)
                
                # Generate mock response
                response = await self.model.generate_response(
                    message=message,
                    conversation_id=conversation_id,
                    max_length=max_length or settings.MAX_LENGTH,
                    temperature=temperature or settings.TEMPERATURE,
                    top_p=top_p or settings.TOP_P
                )
                
                # Save to conversation history
                await self.conversation_manager.add_message(
                    conversation_id,
                    Message(role=MessageRole.USER, content=message)
                )
                await self.conversation_manager.add_message(
                    conversation_id,
                    Message(role=MessageRole.ASSISTANT, content=response)
                )
                
                return response
                
            except Exception as e:
                logger.error(f"Error generating mock response: {e}")
                raise MedGemmaException(f"Failed to generate mock response: {str(e)}")
    
    async def analyze_image(
        self,
        image_data: bytes,
        message: str,
        conversation_id: str,
        max_length: int = None,
        temperature: float = None,
        top_p: float = None
    ) -> str:
        """Analyze an image with mock processing"""
        if not self.is_ready():
            raise MedGemmaException("Mock service not ready")
        
        async with self._inference_lock:
            try:
                # Generate mock image analysis
                response = await self.model.analyze_image(
                    image_data=image_data,
                    message=message,
                    conversation_id=conversation_id,
                    max_length=max_length or settings.MAX_LENGTH,
                    temperature=temperature or settings.TEMPERATURE,
                    top_p=top_p or settings.TOP_P
                )
                
                # Save to conversation history
                await self.conversation_manager.add_message(
                    conversation_id,
                    Message(
                        role=MessageRole.USER,
                        content=[
                            MessageContent(type=ContentType.TEXT, text=message),
                            MessageContent(type=ContentType.IMAGE, image_url="[mock_uploaded_image]")
                        ]
                    )
                )
                await self.conversation_manager.add_message(
                    conversation_id,
                    Message(role=MessageRole.ASSISTANT, content=response)
                )
                
                return response
                
            except Exception as e:
                logger.error(f"Error analyzing mock image: {e}")
                raise MedGemmaException(f"Failed to analyze mock image: {str(e)}")
    
    async def generate_response_stream(
        self,
        message: str,
        conversation_id: str,
        max_length: int = None,
        temperature: float = None,
        top_p: float = None
    ) -> AsyncGenerator[str, None]:
        """Generate mock streaming response"""
        if not self.is_ready():
            raise MedGemmaException("Mock service not ready")
        
        try:
            async for chunk in self.model.generate_response_stream(
                message=message,
                conversation_id=conversation_id,
                max_length=max_length or settings.MAX_LENGTH,
                temperature=temperature or settings.TEMPERATURE,
                top_p=top_p or settings.TOP_P
            ):
                yield chunk
        except Exception as e:
            logger.error(f"Error generating mock streaming response: {e}")
            yield json.dumps({"error": f"Mock streaming failed: {str(e)}"})
    
    async def get_conversation_history(self, conversation_id: str) -> List[Message]:
        """Get conversation history"""
        return await self.conversation_manager.get_conversation(conversation_id)
    
    async def delete_conversation(self, conversation_id: str):
        """Delete conversation"""
        await self.conversation_manager.delete_conversation(conversation_id)
    
    async def cleanup(self):
        """Cleanup mock resources"""
        logger.info("Cleaning up Mock MedGemma service...")
        
        if self.conversation_manager:
            await self.conversation_manager.cleanup()
        
        if self.model and hasattr(self.model, 'cleanup'):
            await self.model.cleanup()
        
        self.model = None
        self._model_loaded = False
        logger.info("Mock MedGemma service cleanup completed")


class SimpleMockModel:
    """Simple fallback mock model if the main mock is not available"""
    
    def __init__(self):
        self.is_loaded = False
        self.responses = [
            "This is a mock response for testing the MedGemma AI Chat application.",
            "Mock medical AI: Please consult with healthcare professionals for real medical advice.",
            "Test response: This demonstrates API functionality without the actual AI model.",
        ]
    
    async def initialize(self):
        """Initialize simple mock"""
        await asyncio.sleep(1)  # Simulate initialization
        self.is_loaded = True
    
    async def generate_response(self, message: str, conversation_id: str, **kwargs) -> str:
        """Generate simple mock response"""
        await asyncio.sleep(0.5)  # Simulate processing
        import random
        response = random.choice(self.responses)
        return f"{response} (Conversation: {conversation_id[:8]}...)"
    
    async def analyze_image(self, image_data: bytes, message: str, conversation_id: str, **kwargs) -> str:
        """Generate simple mock image analysis"""
        await asyncio.sleep(1.0)  # Simulate image processing
        size = len(image_data)
        return f"Mock image analysis: Processed {size} bytes. User query: '{message}'. This is a test response."
    
    async def generate_response_stream(self, message: str, conversation_id: str, **kwargs) -> AsyncGenerator[str, None]:
        """Generate simple mock streaming response"""
        response = await self.generate_response(message, conversation_id, **kwargs)
        words = response.split()
        
        for i, word in enumerate(words):
            chunk_data = {
                "chunk": word + (" " if i < len(words) - 1 else ""),
                "conversation_id": conversation_id
            }
            yield json.dumps(chunk_data)
            await asyncio.sleep(0.1)
    
    async def cleanup(self):
        """Cleanup simple mock"""
        self.is_loaded = False


# Factory function to create the appropriate service
def create_medgemma_service():
    """Create MedGemma service (mock or real based on environment)"""
    if os.getenv('USE_MOCK_MODEL', 'false').lower() == 'true' or os.getenv('ENVIRONMENT') == 'test':
        logger.info("Creating Mock MedGemma service for testing")
        return MockMedGemmaService()
    else:
        # Import and return real service
        from .medgemma_service import MedGemmaService
        logger.info("Creating real MedGemma service")
        return MedGemmaService()
