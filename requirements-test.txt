# Minimal requirements for testing without heavy AI dependencies
# This file is used for testing the application structure without downloading large models

# FastAPI and web framework dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
jinja2==3.1.2

# Basic image processing (lightweight)
pillow>=8.3.2,<10.1.0,!=8.3.*
numpy==1.24.3

# Data handling
pydantic==2.5.0
pydantic-settings==2.1.0

# Database and caching
redis==5.0.1

# Monitoring and logging
prometheus-client==0.19.0
structlog==23.2.0

# HTTP client
httpx==0.25.2
aiofiles==23.2.1
requests==2.31.0

# Environment and configuration
python-dotenv==1.0.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
