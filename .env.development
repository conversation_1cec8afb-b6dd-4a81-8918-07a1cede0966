# MedGemma AI Chat - Development Configuration
# This configuration is for development/testing with dynamic IP addresses

# =============================================================================
# Development Mode Settings
# =============================================================================
ENVIRONMENT=development
DEBUG=true

# =============================================================================
# API Configuration
# =============================================================================
API_KEY=dev-api-key-for-testing-only
HOST=0.0.0.0
PORT=8000

# =============================================================================
# Network Configuration (Development)
# =============================================================================
# Use * for development to allow any origin
CORS_ORIGINS=*
# Allow any host for development
ALLOWED_HOSTS=*

# No domain name required for development
DOMAIN_NAME=localhost
SSL_EMAIL=dev@localhost

# Disable SSL for development
USE_SSL=false
SSL_CERT_PATH=
SSL_KEY_PATH=

# =============================================================================
# Model Configuration
# =============================================================================
MODEL_NAME=google/medgemma-4b-it
MODEL_CACHE_DIR=/app/model_cache
MAX_LENGTH=2048
TEMPERATURE=0.7
TOP_P=0.9

# =============================================================================
# HuggingFace Configuration
# =============================================================================
# You still need a valid HuggingFace token for model access
HUGGINGFACE_TOKEN=your-huggingface-token-here
HF_TOKEN=your-huggingface-token-here

# =============================================================================
# Performance Configuration (Development)
# =============================================================================
WORKERS=1
MAX_CONCURRENT_REQUESTS=5
REQUEST_TIMEOUT=300

# =============================================================================
# File Upload Configuration
# =============================================================================
UPLOAD_MAX_SIZE=10485760
UPLOAD_DIR=/app/uploads
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif,image/bmp,image/tiff

# =============================================================================
# Database Configuration (Development)
# =============================================================================
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=dev-redis-password
CONVERSATION_HISTORY_LIMIT=50
CONVERSATION_TTL=86400

# =============================================================================
# Logging Configuration (Development)
# =============================================================================
LOG_LEVEL=DEBUG
LOG_FORMAT=text
LOG_DIR=/app/logs

# =============================================================================
# Monitoring Configuration
# =============================================================================
ENABLE_METRICS=true
METRICS_PORT=9090

# =============================================================================
# Rate Limiting (Relaxed for Development)
# =============================================================================
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=60

# =============================================================================
# Health Check Configuration
# =============================================================================
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# =============================================================================
# Development Flags
# =============================================================================
# Skip SSL certificate generation
SKIP_SSL=true
# Use HTTP instead of HTTPS
USE_HTTP_ONLY=true
# Allow insecure connections
ALLOW_INSECURE=true
