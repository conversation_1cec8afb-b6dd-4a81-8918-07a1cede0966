<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MedGemma AI Chat</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="title">
                    <span class="icon">🩺</span>
                    MedGemma AI Chat
                </h1>
                <div class="status-indicator">
                    <span id="status-dot" class="status-dot"></span>
                    <span id="status-text">Connecting...</span>
                </div>
            </div>
        </header>

        <!-- Main Chat Interface -->
        <main class="main-content">
            <!-- Chat Messages -->
            <div class="chat-container">
                <div id="chat-messages" class="chat-messages">
                    <div class="welcome-message">
                        <div class="welcome-icon">🤖</div>
                        <h2>Welcome to MedGemma AI</h2>
                        <p>I'm an AI assistant specialized in medical image analysis and healthcare questions. You can:</p>
                        <ul>
                            <li>Ask medical questions</li>
                            <li>Upload medical images for analysis</li>
                            <li>Get explanations about medical conditions</li>
                        </ul>
                        <p class="disclaimer">
                            <strong>Disclaimer:</strong> This AI is for educational purposes only. 
                            Always consult with qualified healthcare professionals for medical advice.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Input Area -->
            <div class="input-area">
                <!-- Image Upload Preview -->
                <div id="image-preview" class="image-preview" style="display: none;">
                    <img id="preview-image" src="" alt="Preview">
                    <button id="remove-image" class="remove-image-btn">&times;</button>
                </div>

                <!-- Input Form -->
                <form id="chat-form" class="chat-form">
                    <div class="input-container">
                        <input type="file" id="image-input" accept="image/*" style="display: none;">
                        <button type="button" id="image-btn" class="image-btn" title="Upload Image">
                            📷
                        </button>
                        <input 
                            type="text" 
                            id="message-input" 
                            placeholder="Type your message or upload an image..." 
                            class="message-input"
                            maxlength="4000"
                        >
                        <button type="submit" id="send-btn" class="send-btn" disabled>
                            <span class="send-icon">➤</span>
                        </button>
                    </div>
                </form>

                <!-- Settings Panel -->
                <div class="settings-panel">
                    <button id="settings-toggle" class="settings-toggle">⚙️</button>
                    <div id="settings-content" class="settings-content" style="display: none;">
                        <div class="setting-group">
                            <label for="temperature">Temperature: <span id="temp-value">0.7</span></label>
                            <input type="range" id="temperature" min="0.1" max="2.0" step="0.1" value="0.7">
                        </div>
                        <div class="setting-group">
                            <label for="max-length">Max Length: <span id="length-value">2048</span></label>
                            <input type="range" id="max-length" min="512" max="4096" step="256" value="2048">
                        </div>
                        <div class="setting-group">
                            <button id="clear-chat" class="clear-btn">Clear Chat</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Loading Overlay -->
        <div id="loading-overlay" class="loading-overlay" style="display: none;">
            <div class="loading-spinner"></div>
            <p>Processing your request...</p>
        </div>

        <!-- Error Modal -->
        <div id="error-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h3>Error</h3>
                <p id="error-message"></p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="script.js"></script>
</body>
</html>
