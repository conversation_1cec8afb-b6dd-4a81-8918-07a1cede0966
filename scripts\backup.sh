#!/bin/bash

# MedGemma AI Chat Backup Script
# Backs up configuration, data, and logs

set -e

# Configuration
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=7

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Create backup directory
create_backup_dir() {
    log "Creating backup directory..."
    mkdir -p "$BACKUP_DIR"
    
    if [[ ! -w "$BACKUP_DIR" ]]; then
        error "Backup directory is not writable: $BACKUP_DIR"
    fi
}

# Backup configuration files
backup_config() {
    log "Backing up configuration files..."
    
    CONFIG_BACKUP="$BACKUP_DIR/medgemma_config_$DATE.tar.gz"
    
    tar -czf "$CONFIG_BACKUP" \
        --exclude='*.log' \
        --exclude='__pycache__' \
        --exclude='.git' \
        --exclude='model_cache' \
        --exclude='uploads' \
        .env \
        docker-compose.yml \
        nginx/ \
        frontend/ \
        app/ \
        scripts/ \
        docs/ \
        monitoring/ \
        requirements.txt \
        README.md \
        2>/dev/null || warn "Some files may not exist"
    
    if [[ -f "$CONFIG_BACKUP" ]]; then
        log "Configuration backup created: $CONFIG_BACKUP"
        log "Size: $(du -h "$CONFIG_BACKUP" | cut -f1)"
    else
        error "Failed to create configuration backup"
    fi
}

# Backup Redis data
backup_redis() {
    log "Backing up Redis data..."
    
    # Check if Redis container is running
    if ! docker-compose ps redis | grep -q "Up"; then
        warn "Redis container is not running, skipping Redis backup"
        return
    fi
    
    # Trigger Redis background save
    docker-compose exec -T redis redis-cli BGSAVE >/dev/null 2>&1 || warn "Failed to trigger Redis BGSAVE"
    
    # Wait for background save to complete
    sleep 5
    
    # Copy Redis dump file
    REDIS_BACKUP="$BACKUP_DIR/redis_$DATE.rdb"
    if docker cp medgemma-redis:/data/dump.rdb "$REDIS_BACKUP" 2>/dev/null; then
        log "Redis backup created: $REDIS_BACKUP"
        log "Size: $(du -h "$REDIS_BACKUP" | cut -f1)"
    else
        warn "Failed to backup Redis data"
    fi
}

# Backup logs
backup_logs() {
    log "Backing up logs..."
    
    LOGS_BACKUP="$BACKUP_DIR/logs_$DATE.tar.gz"
    
    # Create logs backup if logs exist
    if docker volume inspect docker-medgemma-fastapi_logs >/dev/null 2>&1; then
        # Create temporary container to access logs volume
        docker run --rm \
            -v docker-medgemma-fastapi_logs:/logs:ro \
            -v "$BACKUP_DIR":/backup \
            alpine:latest \
            tar -czf "/backup/logs_$DATE.tar.gz" -C /logs . 2>/dev/null || warn "Failed to backup logs"
        
        if [[ -f "$LOGS_BACKUP" ]]; then
            log "Logs backup created: $LOGS_BACKUP"
            log "Size: $(du -h "$LOGS_BACKUP" | cut -f1)"
        fi
    else
        warn "Logs volume not found, skipping logs backup"
    fi
}

# Backup uploaded files
backup_uploads() {
    log "Backing up uploaded files..."
    
    UPLOADS_BACKUP="$BACKUP_DIR/uploads_$DATE.tar.gz"
    
    # Create uploads backup if uploads exist
    if docker volume inspect docker-medgemma-fastapi_upload_data >/dev/null 2>&1; then
        # Create temporary container to access uploads volume
        docker run --rm \
            -v docker-medgemma-fastapi_upload_data:/uploads:ro \
            -v "$BACKUP_DIR":/backup \
            alpine:latest \
            tar -czf "/backup/uploads_$DATE.tar.gz" -C /uploads . 2>/dev/null || warn "Failed to backup uploads"
        
        if [[ -f "$UPLOADS_BACKUP" ]]; then
            log "Uploads backup created: $UPLOADS_BACKUP"
            log "Size: $(du -h "$UPLOADS_BACKUP" | cut -f1)"
        fi
    else
        warn "Uploads volume not found, skipping uploads backup"
    fi
}

# Create backup manifest
create_manifest() {
    log "Creating backup manifest..."
    
    MANIFEST="$BACKUP_DIR/backup_manifest_$DATE.txt"
    
    cat > "$MANIFEST" << EOF
MedGemma AI Chat Backup Manifest
Generated: $(date)
Backup Date: $DATE

=== System Information ===
Hostname: $(hostname)
OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)
Docker Version: $(docker --version)
Docker Compose Version: $(docker-compose --version)

=== Service Status ===
$(docker-compose ps)

=== Backup Files ===
EOF
    
    # List backup files
    find "$BACKUP_DIR" -name "*_$DATE.*" -type f -exec basename {} \; | while read file; do
        echo "- $file ($(du -h "$BACKUP_DIR/$file" | cut -f1))" >> "$MANIFEST"
    done
    
    log "Backup manifest created: $MANIFEST"
}

# Cleanup old backups
cleanup_old_backups() {
    log "Cleaning up old backups (older than $RETENTION_DAYS days)..."
    
    # Find and delete old backup files
    DELETED_COUNT=0
    find "$BACKUP_DIR" -name "medgemma_*" -type f -mtime +$RETENTION_DAYS | while read file; do
        rm -f "$file"
        DELETED_COUNT=$((DELETED_COUNT + 1))
        log "Deleted old backup: $(basename "$file")"
    done
    
    find "$BACKUP_DIR" -name "redis_*" -type f -mtime +$RETENTION_DAYS | while read file; do
        rm -f "$file"
        DELETED_COUNT=$((DELETED_COUNT + 1))
        log "Deleted old backup: $(basename "$file")"
    done
    
    find "$BACKUP_DIR" -name "logs_*" -type f -mtime +$RETENTION_DAYS | while read file; do
        rm -f "$file"
        DELETED_COUNT=$((DELETED_COUNT + 1))
        log "Deleted old backup: $(basename "$file")"
    done
    
    find "$BACKUP_DIR" -name "uploads_*" -type f -mtime +$RETENTION_DAYS | while read file; do
        rm -f "$file"
        DELETED_COUNT=$((DELETED_COUNT + 1))
        log "Deleted old backup: $(basename "$file")"
    done
    
    find "$BACKUP_DIR" -name "backup_manifest_*" -type f -mtime +$RETENTION_DAYS | while read file; do
        rm -f "$file"
        DELETED_COUNT=$((DELETED_COUNT + 1))
        log "Deleted old manifest: $(basename "$file")"
    done
    
    log "Cleanup completed"
}

# Calculate total backup size
calculate_backup_size() {
    log "Calculating backup size..."
    
    TOTAL_SIZE=$(find "$BACKUP_DIR" -name "*_$DATE.*" -type f -exec du -b {} + | awk '{sum += $1} END {print sum}')
    
    if [[ -n "$TOTAL_SIZE" ]]; then
        # Convert bytes to human readable format
        if [[ $TOTAL_SIZE -gt 1073741824 ]]; then
            SIZE_DISPLAY="$(echo "scale=2; $TOTAL_SIZE / 1073741824" | bc)GB"
        elif [[ $TOTAL_SIZE -gt 1048576 ]]; then
            SIZE_DISPLAY="$(echo "scale=2; $TOTAL_SIZE / 1048576" | bc)MB"
        elif [[ $TOTAL_SIZE -gt 1024 ]]; then
            SIZE_DISPLAY="$(echo "scale=2; $TOTAL_SIZE / 1024" | bc)KB"
        else
            SIZE_DISPLAY="${TOTAL_SIZE}B"
        fi
        
        log "Total backup size: $SIZE_DISPLAY"
    fi
}

# Main backup function
main() {
    log "Starting MedGemma AI Chat backup..."
    
    # Check if we're in the right directory
    if [[ ! -f docker-compose.yml ]]; then
        error "docker-compose.yml not found. Please run this script from the project directory."
    fi
    
    create_backup_dir
    backup_config
    backup_redis
    backup_logs
    backup_uploads
    create_manifest
    calculate_backup_size
    cleanup_old_backups
    
    log "Backup completed successfully!"
    log "Backup location: $BACKUP_DIR"
    log "Backup date: $DATE"
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
