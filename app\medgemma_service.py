"""
MedGemma AI Service for handling model inference
"""

import asyncio
import json
import time
from typing import Optional, List, Dict, Any, AsyncGenerator
from io import BytesIO
import torch
from PIL import Image
import structlog
from transformers import AutoProcessor, AutoModelForImageTextToText, pipeline
from transformers.utils import logging as transformers_logging

from .config import settings
from .models import Message, MessageRole, ContentType, MessageContent
from .conversation_manager import ConversationManager
from .exceptions import MedGemmaException

# Suppress transformers warnings
transformers_logging.set_verbosity_error()

logger = structlog.get_logger()


class MedGemmaService:
    """Service for handling MedGemma model inference"""
    
    def __init__(self):
        self.model = None
        self.processor = None
        self.pipeline = None
        self.conversation_manager = ConversationManager()
        self._model_loaded = False
        self._initialization_lock = asyncio.Lock()
        self._inference_lock = asyncio.Semaphore(settings.MAX_CONCURRENT_REQUESTS)
        
    async def initialize(self):
        """Initialize the MedGemma model and processor"""
        async with self._initialization_lock:
            if self._model_loaded:
                return
                
            logger.info("Initializing MedGemma model...")
            start_time = time.time()
            
            try:
                # Set device (CPU for t3.xlarge)
                device = "cuda" if torch.cuda.is_available() else "cpu"
                logger.info(f"Using device: {device}")
                
                # Load model and processor
                await self._load_model(device)
                
                # Initialize conversation manager
                await self.conversation_manager.initialize()
                
                self._model_loaded = True
                load_time = time.time() - start_time
                logger.info(f"MedGemma model loaded successfully in {load_time:.2f} seconds")
                
            except Exception as e:
                logger.error(f"Failed to initialize MedGemma model: {e}")
                raise MedGemmaException(f"Model initialization failed: {str(e)}")
    
    async def _load_model(self, device: str):
        """Load the MedGemma model and processor"""
        try:
            # Load processor
            logger.info("Loading processor...")
            self.processor = AutoProcessor.from_pretrained(
                settings.MODEL_NAME,
                cache_dir=settings.MODEL_CACHE_DIR,
                trust_remote_code=True
            )
            
            # Load model with CPU optimizations
            logger.info("Loading model...")
            self.model = AutoModelForImageTextToText.from_pretrained(
                settings.MODEL_NAME,
                cache_dir=settings.MODEL_CACHE_DIR,
                torch_dtype=torch.float32 if device == "cpu" else torch.bfloat16,
                device_map="auto" if device == "cuda" else None,
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )
            
            if device == "cpu":
                self.model = self.model.to(device)
            
            # Create pipeline for easier inference
            self.pipeline = pipeline(
                "image-text-to-text",
                model=self.model,
                processor=self.processor,
                torch_dtype=torch.float32 if device == "cpu" else torch.bfloat16,
                device=device
            )
            
            logger.info("Model and processor loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise
    
    def is_ready(self) -> bool:
        """Check if the service is ready"""
        return self._model_loaded and self.model is not None
    
    def is_model_loaded(self) -> bool:
        """Check if the model is loaded"""
        return self._model_loaded
    
    async def generate_response(
        self,
        message: str,
        conversation_id: str,
        max_length: int = None,
        temperature: float = None,
        top_p: float = None
    ) -> str:
        """Generate a text response"""
        if not self.is_ready():
            raise MedGemmaException("Service not ready")
        
        async with self._inference_lock:
            try:
                # Get conversation history
                history = await self.conversation_manager.get_conversation(conversation_id)
                
                # Prepare messages
                messages = self._prepare_text_messages(message, history)
                
                # Generate response
                response = await self._generate_with_pipeline(
                    messages=messages,
                    max_length=max_length or settings.MAX_LENGTH,
                    temperature=temperature or settings.TEMPERATURE,
                    top_p=top_p or settings.TOP_P
                )
                
                # Save to conversation history
                await self.conversation_manager.add_message(
                    conversation_id,
                    Message(role=MessageRole.USER, content=message)
                )
                await self.conversation_manager.add_message(
                    conversation_id,
                    Message(role=MessageRole.ASSISTANT, content=response)
                )
                
                return response
                
            except Exception as e:
                logger.error(f"Error generating response: {e}")
                raise MedGemmaException(f"Failed to generate response: {str(e)}")
    
    async def analyze_image(
        self,
        image_data: bytes,
        message: str,
        conversation_id: str,
        max_length: int = None,
        temperature: float = None,
        top_p: float = None
    ) -> str:
        """Analyze an image with text prompt"""
        if not self.is_ready():
            raise MedGemmaException("Service not ready")
        
        async with self._inference_lock:
            try:
                # Process image
                image = Image.open(BytesIO(image_data)).convert('RGB')
                
                # Get conversation history
                history = await self.conversation_manager.get_conversation(conversation_id)
                
                # Prepare messages with image
                messages = self._prepare_image_messages(message, image, history)
                
                # Generate response
                response = await self._generate_with_pipeline(
                    messages=messages,
                    image=image,
                    max_length=max_length or settings.MAX_LENGTH,
                    temperature=temperature or settings.TEMPERATURE,
                    top_p=top_p or settings.TOP_P
                )
                
                # Save to conversation history
                await self.conversation_manager.add_message(
                    conversation_id,
                    Message(
                        role=MessageRole.USER,
                        content=[
                            MessageContent(type=ContentType.TEXT, text=message),
                            MessageContent(type=ContentType.IMAGE, image_url="[uploaded_image]")
                        ]
                    )
                )
                await self.conversation_manager.add_message(
                    conversation_id,
                    Message(role=MessageRole.ASSISTANT, content=response)
                )
                
                return response
                
            except Exception as e:
                logger.error(f"Error analyzing image: {e}")
                raise MedGemmaException(f"Failed to analyze image: {str(e)}")
    
    async def generate_response_stream(
        self,
        message: str,
        conversation_id: str,
        max_length: int = None,
        temperature: float = None,
        top_p: float = None
    ) -> AsyncGenerator[str, None]:
        """Generate streaming response"""
        if not self.is_ready():
            raise MedGemmaException("Service not ready")
        
        # For now, simulate streaming by yielding the full response
        # In a production environment, you might implement true streaming
        response = await self.generate_response(
            message, conversation_id, max_length, temperature, top_p
        )
        
        # Simulate streaming by yielding chunks
        words = response.split()
        for i, word in enumerate(words):
            chunk = word + (" " if i < len(words) - 1 else "")
            yield json.dumps({"chunk": chunk, "conversation_id": conversation_id})
            await asyncio.sleep(0.05)  # Small delay to simulate streaming
    
    def _prepare_text_messages(self, message: str, history: List[Message]) -> List[Dict]:
        """Prepare messages for text-only inference"""
        messages = []
        
        # Add system message
        messages.append({
            "role": "system",
            "content": [{"type": "text", "text": "You are an expert medical AI assistant."}]
        })
        
        # Add conversation history (limited)
        for msg in history[-10:]:  # Last 10 messages
            if isinstance(msg.content, str):
                messages.append({
                    "role": msg.role.value,
                    "content": [{"type": "text", "text": msg.content}]
                })
        
        # Add current message
        messages.append({
            "role": "user",
            "content": [{"type": "text", "text": message}]
        })
        
        return messages
    
    def _prepare_image_messages(self, message: str, image: Image.Image, history: List[Message]) -> List[Dict]:
        """Prepare messages for image analysis"""
        messages = []
        
        # Add system message
        messages.append({
            "role": "system",
            "content": [{"type": "text", "text": "You are an expert radiologist and medical AI assistant."}]
        })
        
        # Add current message with image
        messages.append({
            "role": "user",
            "content": [
                {"type": "text", "text": message},
                {"type": "image", "image": image}
            ]
        })
        
        return messages
    
    async def _generate_with_pipeline(
        self,
        messages: List[Dict],
        image: Optional[Image.Image] = None,
        max_length: int = 2048,
        temperature: float = 0.7,
        top_p: float = 0.9
    ) -> str:
        """Generate response using the pipeline"""
        try:
            # Run inference in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            
            def run_inference():
                output = self.pipeline(
                    text=messages,
                    max_new_tokens=max_length,
                    temperature=temperature,
                    top_p=top_p,
                    do_sample=True
                )
                return output[0]["generated_text"][-1]["content"]
            
            response = await loop.run_in_executor(None, run_inference)
            return response
            
        except Exception as e:
            logger.error(f"Pipeline inference error: {e}")
            raise
    
    async def get_conversation_history(self, conversation_id: str) -> List[Message]:
        """Get conversation history"""
        return await self.conversation_manager.get_conversation(conversation_id)
    
    async def delete_conversation(self, conversation_id: str):
        """Delete conversation"""
        await self.conversation_manager.delete_conversation(conversation_id)
    
    async def cleanup(self):
        """Cleanup resources"""
        logger.info("Cleaning up MedGemma service...")
        
        if self.conversation_manager:
            await self.conversation_manager.cleanup()
        
        # Clear model from memory
        if self.model:
            del self.model
            self.model = None
        
        if self.processor:
            del self.processor
            self.processor = None
        
        if self.pipeline:
            del self.pipeline
            self.pipeline = None
        
        # Clear CUDA cache if available
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        self._model_loaded = False
        logger.info("MedGemma service cleanup completed")
