#!/bin/bash

# Quick configuration validation script
# Tests nginx config and basic setup without full deployment

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== Quick Configuration Validation ===${NC}"
echo

# Test 1: Nginx configuration syntax
echo -e "${BLUE}Testing nginx configuration...${NC}"
nginx_test_output=$(docker run --rm -v "$(pwd)/nginx/nginx.dev.conf:/etc/nginx/nginx.conf:ro" nginx:alpine nginx -t 2>&1)
if echo "$nginx_test_output" | grep -q "syntax is ok" && echo "$nginx_test_output" | grep -q "test is successful"; then
    echo -e "${GREEN}✅ Nginx development config: VALID${NC}"
elif echo "$nginx_test_output" | grep -q "Configuration complete"; then
    echo -e "${GREEN}✅ Nginx development config: VALID (startup successful)${NC}"
else
    echo -e "${RED}❌ Nginx development config: INVALID${NC}"
    echo "Error output: $nginx_test_output"
fi

# Test 2: Docker Compose syntax
echo -e "${BLUE}Testing Docker Compose configuration...${NC}"
if docker-compose -f docker-compose.dev.yml config >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Docker Compose config: VALID${NC}"
else
    echo -e "${RED}❌ Docker Compose config: INVALID${NC}"
fi

# Test 3: Environment file
echo -e "${BLUE}Testing environment configuration...${NC}"
if [[ -f .env.development ]]; then
    echo -e "${GREEN}✅ Development environment file: EXISTS${NC}"
    
    # Check for required variables
    if grep -q "HUGGINGFACE_TOKEN=" .env.development; then
        echo -e "${GREEN}✅ HuggingFace token placeholder: FOUND${NC}"
    else
        echo -e "${RED}❌ HuggingFace token placeholder: MISSING${NC}"
    fi
else
    echo -e "${RED}❌ Development environment file: MISSING${NC}"
fi

# Test 4: Required files
echo -e "${BLUE}Testing required files...${NC}"
required_files=(
    "docker-compose.dev.yml"
    "docker/Dockerfile.medgemma"
    "docker/Dockerfile.nginx.dev"
    "app/main.py"
    "frontend/index.html"
    "scripts/mock-model.py"
)

all_files_exist=true
for file in "${required_files[@]}"; do
    if [[ -f "$file" ]]; then
        echo -e "${GREEN}✅ $file: EXISTS${NC}"
    else
        echo -e "${RED}❌ $file: MISSING${NC}"
        all_files_exist=false
    fi
done

# Test 5: Docker availability
echo -e "${BLUE}Testing Docker...${NC}"
if command -v docker >/dev/null 2>&1; then
    if docker info >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Docker: AVAILABLE${NC}"
    else
        echo -e "${RED}❌ Docker daemon: NOT RUNNING${NC}"
    fi
else
    echo -e "${RED}❌ Docker: NOT INSTALLED${NC}"
fi

echo
if [[ "$all_files_exist" == true ]]; then
    echo -e "${GREEN}🎉 Basic validation completed successfully!${NC}"
    echo -e "${BLUE}Next step: Run './scripts/run-tests.sh quick' to test builds${NC}"
else
    echo -e "${RED}❌ Some required files are missing${NC}"
    echo -e "${YELLOW}Please ensure all project files are present${NC}"
fi
