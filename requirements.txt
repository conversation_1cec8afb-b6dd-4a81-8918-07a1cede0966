# FastAPI and web framework dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
jinja2==3.1.2

# AI/ML dependencies
transformers==4.50.0
torch==2.1.0
torchvision==0.16.0
torchaudio==2.1.0
accelerate==0.24.1
optimum==1.14.1
bitsandbytes==0.41.3
# Pillow version compatible with both torchvision and imageio
pillow>=8.3.2,<10.1.0,!=8.3.*
numpy==1.24.3
scipy==1.11.4

# Image processing
opencv-python-headless==********
# Updated imageio to version compatible with Pillow
imageio>=2.31.6,<2.35.0

# Data handling
pandas==2.1.3
pydantic==2.5.0
pydantic-settings==2.1.0

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Database and caching
redis==5.0.1
sqlalchemy==2.0.23
alembic==1.12.1

# Monitoring and logging
prometheus-client==0.19.0
structlog==23.2.0
python-json-logger==2.0.7

# HTTP client
httpx==0.25.2
aiofiles==23.2.1
requests==2.31.0

# Environment and configuration
python-dotenv==1.0.0
pyyaml==6.0.1

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Production server
gunicorn==21.2.0
