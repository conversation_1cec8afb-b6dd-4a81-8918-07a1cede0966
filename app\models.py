"""
Pydantic models for MedGemma API
"""

from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum


class MessageRole(str, Enum):
    """Message roles in conversation"""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"


class ContentType(str, Enum):
    """Content types for messages"""
    TEXT = "text"
    IMAGE = "image"


class MessageContent(BaseModel):
    """Content within a message"""
    type: ContentType
    text: Optional[str] = None
    image_url: Optional[str] = None
    
    @validator('text')
    def validate_text_content(cls, v, values):
        if values.get('type') == ContentType.TEXT and not v:
            raise ValueError('Text content cannot be empty for text type')
        return v
    
    @validator('image_url')
    def validate_image_content(cls, v, values):
        if values.get('type') == ContentType.IMAGE and not v:
            raise ValueError('Image URL cannot be empty for image type')
        return v


class Message(BaseModel):
    """Individual message in conversation"""
    role: MessageRole
    content: Union[str, List[MessageContent]]
    timestamp: Optional[datetime] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ChatRequest(BaseModel):
    """Request model for chat endpoint"""
    message: str = Field(..., min_length=1, max_length=4000, description="User message")
    conversation_id: Optional[str] = Field(None, description="Conversation ID for context")
    max_length: Optional[int] = Field(2048, ge=1, le=4096, description="Maximum response length")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="Generation temperature")
    top_p: Optional[float] = Field(0.9, ge=0.0, le=1.0, description="Top-p sampling parameter")
    system_prompt: Optional[str] = Field(None, max_length=1000, description="System prompt override")
    
    @validator('message')
    def validate_message(cls, v):
        if not v.strip():
            raise ValueError('Message cannot be empty or whitespace only')
        return v.strip()


class ChatResponse(BaseModel):
    """Response model for chat endpoint"""
    response: str = Field(..., description="AI response")
    conversation_id: str = Field(..., description="Conversation ID")
    model: str = Field(..., description="Model used for generation")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")
    usage: Optional[Dict[str, int]] = Field(None, description="Token usage statistics")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ImageAnalysisRequest(BaseModel):
    """Request model for image analysis"""
    message: str = Field(..., min_length=1, max_length=2000, description="Analysis prompt")
    conversation_id: Optional[str] = Field(None, description="Conversation ID for context")
    max_length: Optional[int] = Field(2048, ge=1, le=4096, description="Maximum response length")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="Generation temperature")
    top_p: Optional[float] = Field(0.9, ge=0.0, le=1.0, description="Top-p sampling parameter")
    
    @validator('message')
    def validate_message(cls, v):
        if not v.strip():
            raise ValueError('Message cannot be empty or whitespace only')
        return v.strip()


class ConversationHistory(BaseModel):
    """Conversation history model"""
    conversation_id: str
    messages: List[Message]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class HealthResponse(BaseModel):
    """Health check response model"""
    status: str = Field(..., description="Service status")
    model_loaded: bool = Field(..., description="Whether model is loaded")
    version: str = Field(..., description="API version")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Health check timestamp")
    uptime: Optional[float] = Field(None, description="Service uptime in seconds")
    memory_usage: Optional[Dict[str, float]] = Field(None, description="Memory usage statistics")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error message")
    error_code: Optional[str] = Field(None, description="Error code")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ModelInfo(BaseModel):
    """Model information"""
    name: str = Field(..., description="Model name")
    version: str = Field(..., description="Model version")
    description: Optional[str] = Field(None, description="Model description")
    capabilities: List[str] = Field(default_factory=list, description="Model capabilities")
    max_context_length: int = Field(..., description="Maximum context length")
    supported_languages: List[str] = Field(default_factory=list, description="Supported languages")


class UsageStats(BaseModel):
    """Usage statistics"""
    total_requests: int = Field(0, description="Total number of requests")
    successful_requests: int = Field(0, description="Number of successful requests")
    failed_requests: int = Field(0, description="Number of failed requests")
    average_response_time: float = Field(0.0, description="Average response time in seconds")
    total_tokens_generated: int = Field(0, description="Total tokens generated")
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate"""
        if self.total_requests == 0:
            return 0.0
        return (self.successful_requests / self.total_requests) * 100


class StreamingResponse(BaseModel):
    """Streaming response chunk"""
    chunk: str = Field(..., description="Response chunk")
    conversation_id: str = Field(..., description="Conversation ID")
    is_complete: bool = Field(False, description="Whether this is the final chunk")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Chunk timestamp")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class RateLimitInfo(BaseModel):
    """Rate limit information"""
    requests_remaining: int = Field(..., description="Requests remaining in current window")
    reset_time: datetime = Field(..., description="When the rate limit resets")
    limit: int = Field(..., description="Total requests allowed per window")
    window_seconds: int = Field(..., description="Rate limit window in seconds")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class SystemStatus(BaseModel):
    """System status information"""
    status: str = Field(..., description="Overall system status")
    services: Dict[str, str] = Field(default_factory=dict, description="Individual service statuses")
    version: str = Field(..., description="System version")
    uptime: float = Field(..., description="System uptime in seconds")
    load_average: Optional[List[float]] = Field(None, description="System load average")
    memory_usage: Optional[Dict[str, float]] = Field(None, description="Memory usage")
    disk_usage: Optional[Dict[str, float]] = Field(None, description="Disk usage")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
