# Development Nginx Dockerfile (HTTP only, no SSL)
FROM nginx:alpine

# Copy development nginx configuration
COPY nginx/nginx.dev.conf /etc/nginx/nginx.conf
COPY nginx/conf.d/medgemma.dev.conf /etc/nginx/conf.d/default.conf

# Copy frontend files
COPY frontend/ /usr/share/nginx/html/

# Note: nginx:alpine already includes nginx user (UID 101) and nginx group (GID 101)
# So we don't need to create them, just ensure proper permissions

# Set proper permissions for existing nginx user
RUN chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    chown -R nginx:nginx /usr/share/nginx/html && \
    touch /var/run/nginx.pid && \
    chown -R nginx:nginx /var/run/nginx.pid

# Expose only HTTP port for development
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
