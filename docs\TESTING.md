# MedGemma AI Chat - Testing Guide

This guide explains how to test the MedGemma AI Chat application without downloading the actual 4GB+ model, saving time and resources during development and validation.

## 🎯 Testing Overview

The testing framework validates the entire deployment process using lightweight mock models instead of the actual MedGemma-4b-it model. This approach:

- ✅ **Validates all infrastructure** (Docker, nginx, Redis, networking)
- ✅ **Tests API endpoints** with mock responses
- ✅ **Verifies configuration** and file structure
- ✅ **Checks service integration** and connectivity
- ✅ **Completes in under 10 minutes** (vs hours for model download)
- ✅ **Provides confidence** that full deployment will succeed

## 🚀 Quick Start

### Run Complete Test Suite
```bash
# Run all tests (recommended)
./scripts/test-deployment.sh

# Or use the test runner
./scripts/run-tests.sh full
```

### Quick Validation
```bash
# Fast validation (build + config only)
./scripts/run-tests.sh quick
```

### Individual Test Components
```bash
# Test only Docker builds
./scripts/run-tests.sh build

# Test only configuration
./scripts/run-tests.sh config

# Test only API endpoints
./scripts/run-tests.sh api

# Test mock model functionality
./scripts/run-tests.sh mock
```

## 📋 Test Components

### 1. Prerequisites Check
- ✅ Docker installation and daemon status
- ✅ Docker Compose availability
- ✅ Project directory structure
- ✅ Required files presence

### 2. Configuration Testing
- ✅ Environment variable validation
- ✅ Docker Compose syntax check
- ✅ Nginx configuration syntax
- ✅ Python requirements validation

### 3. Docker Build Testing
- ✅ MedGemma API container build (with mock model)
- ✅ Nginx development container build
- ✅ Redis image pull
- ✅ Complete build process validation

### 4. Service Integration Testing
- ✅ Service startup and health status
- ✅ Container networking and connectivity
- ✅ Redis connectivity and persistence
- ✅ Nginx static file serving

### 5. API Endpoint Testing
- ✅ Health check endpoint
- ✅ Metrics endpoint
- ✅ Chat endpoint with mock responses
- ✅ Image upload and analysis (mock processing)
- ✅ Streaming responses

### 6. Infrastructure Testing
- ✅ Volume persistence across restarts
- ✅ Network connectivity between containers
- ✅ Logging configuration
- ✅ Graceful service shutdown

## 🔧 Test Scripts

### Main Test Script
**`scripts/test-deployment.sh`** - Comprehensive test suite

```bash
# Full test suite
./scripts/test-deployment.sh

# Individual components
./scripts/test-deployment.sh --build-only
./scripts/test-deployment.sh --config-only
./scripts/test-deployment.sh --api-only

# Cleanup
./scripts/test-deployment.sh --cleanup
```

### Test Runner
**`scripts/run-tests.sh`** - Easy test execution

```bash
./scripts/run-tests.sh [OPTION]

Options:
  full      Complete test suite
  build     Docker builds only
  config    Configuration only
  api       API endpoints only
  mock      Mock model testing
  quick     Quick validation
  cleanup   Clean up test environment
```

### Mock Model
**`scripts/mock-model.py`** - Lightweight model simulation

```bash
# Test mock model directly
python3 scripts/mock-model.py

# Or through test runner
./scripts/run-tests.sh mock
```

## 🧪 Mock Model Features

The mock model provides realistic simulation of MedGemma functionality:

### Text Generation
- Medical query classification (symptoms, medications, emergencies)
- Contextual responses based on query type
- Parameter simulation (temperature, max_length)
- Conversation history support

### Image Analysis
- Mock medical image processing
- File size and format validation
- Simulated analysis responses
- User query integration

### Streaming Responses
- Word-by-word streaming simulation
- JSON chunk formatting
- Realistic timing delays
- Error handling

### Model Information
- Capability reporting
- Usage statistics
- Performance metrics
- Version information

## 📊 Test Output

### Success Example
```
=== Prerequisites Check ===
✅ PASS: Docker installation
✅ PASS: Docker Compose installation
✅ PASS: Docker daemon running
✅ PASS: Project directory structure

=== Configuration Testing ===
✅ PASS: Environment configuration
✅ PASS: Docker Compose configuration syntax
✅ PASS: Nginx configuration syntax
✅ PASS: Requirements.txt validation

=== Test Summary ===
📊 Test Results Summary
==========================
Total Tests: 20
Passed: 20
Failed: 0
Skipped: 0

🎉 All tests passed!
Your deployment configuration is ready for production use.
```

### Failure Example
```
=== Docker Build Testing ===
❌ FAIL: MedGemma API build (with mock model) - Build failed
✅ PASS: Nginx development build
✅ PASS: Redis image pull

=== Test Summary ===
📊 Test Results Summary
==========================
Total Tests: 15
Passed: 12
Failed: 3
Skipped: 0

❌ Some tests failed. Please review the issues above.
Fix the failing tests before proceeding with deployment.
```

## 🔍 Troubleshooting Tests

### Common Test Failures

#### Docker Build Failures
```bash
# Clean Docker cache and retry
docker system prune -f
./scripts/test-deployment.sh --build-only
```

#### Configuration Issues
```bash
# Check environment file
cat .env.development

# Validate Docker Compose
docker-compose -f docker-compose.dev.yml config
```

#### Service Connectivity Issues
```bash
# Check container status
docker-compose -f docker-compose.test.yml ps

# View logs
docker-compose -f docker-compose.test.yml logs
```

#### Port Conflicts
```bash
# Check for port usage
netstat -tulpn | grep -E ':(80|8000|8080|6379)'

# Stop conflicting services
sudo systemctl stop nginx  # If system nginx is running
```

### Test Environment Cleanup

```bash
# Automatic cleanup
./scripts/test-deployment.sh --cleanup

# Manual cleanup
docker-compose -f docker-compose.test.yml down -v
docker system prune -f
rm -f .env.test docker-compose.test.yml
```

## 🎯 Test Validation Levels

### Level 1: Quick Validation (2-3 minutes)
- Configuration syntax
- Docker builds
- Basic file structure

```bash
./scripts/run-tests.sh quick
```

### Level 2: Integration Testing (5-7 minutes)
- Service startup
- API connectivity
- Basic functionality

```bash
./scripts/run-tests.sh api
```

### Level 3: Complete Validation (8-10 minutes)
- Full infrastructure testing
- End-to-end workflows
- Performance validation

```bash
./scripts/run-tests.sh full
```

## 🔄 CI/CD Integration

### GitHub Actions Example
```yaml
name: Test MedGemma Deployment
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run deployment tests
        run: ./scripts/test-deployment.sh
```

### Pre-deployment Validation
```bash
# Before deploying to production
./scripts/test-deployment.sh

# If tests pass, proceed with deployment
if [ $? -eq 0 ]; then
    ./scripts/deploy-dev.sh
fi
```

## 📈 Performance Benchmarks

### Test Execution Times
- **Prerequisites Check**: 10-15 seconds
- **Configuration Testing**: 15-20 seconds
- **Docker Builds**: 2-4 minutes
- **Service Integration**: 1-2 minutes
- **API Testing**: 1-2 minutes
- **Infrastructure Testing**: 1-2 minutes

**Total Time**: 6-10 minutes (vs 30+ minutes with real model)

### Resource Usage
- **Memory**: ~2GB (vs 14GB+ with real model)
- **Disk**: ~500MB (vs 10GB+ with real model)
- **CPU**: Minimal during testing

## 🎉 Benefits of Mock Testing

### Development Benefits
- ⚡ **Fast iteration** - Quick validation cycles
- 💰 **Cost effective** - Reduced compute requirements
- 🔧 **Easy debugging** - Predictable mock responses
- 🚀 **CI/CD friendly** - Fast automated testing

### Deployment Benefits
- ✅ **High confidence** - Validates everything except model
- 🛡️ **Risk reduction** - Catch issues before production
- 📊 **Clear reporting** - Detailed pass/fail status
- 🔄 **Repeatable** - Consistent test results

### Production Readiness
- 🏗️ **Infrastructure validated** - All components tested
- 🌐 **Networking verified** - Service communication confirmed
- 🔒 **Security checked** - Authentication and authorization
- 📈 **Performance baseline** - Response time measurements

## 📞 Support

### Test Issues
- Check [TROUBLESHOOTING.md](TROUBLESHOOTING.md) for common solutions
- Review test logs for specific error messages
- Ensure system meets prerequisites

### Getting Help
- Run tests with verbose output: `./scripts/test-deployment.sh --verbose`
- Check individual components: `./scripts/run-tests.sh [component]`
- Clean environment and retry: `./scripts/test-deployment.sh --cleanup`

The testing framework provides comprehensive validation of your MedGemma AI Chat deployment, ensuring everything works correctly before investing time in downloading and configuring the actual AI model.
