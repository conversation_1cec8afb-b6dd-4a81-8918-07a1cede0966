"""
Main FastAPI application for MedGemma AI Chat Service
"""

import os
import logging
import asyncio
from contextlib import asynccontextmanager
from typing import Optional

from fastapi import FastAP<PERSON>, HTTPException, Depends, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import StreamingResponse, JSONResponse, Response
from prometheus_client import Counter, Histogram, generate_latest, CONTENT_TYPE_LATEST
import structlog

from .config import settings
from .models import ChatRequest, ChatResponse, HealthResponse, ImageAnalysisRequest
from .medgemma_service import MedGemmaService
from .auth import verify_api_key
from .utils import setup_logging, create_conversation_id
from .exceptions import MedGemmaException, handle_medgemma_exception

# Setup logging
setup_logging()
logger = structlog.get_logger()

# Prometheus metrics
REQUEST_COUNT = Counter('medgemma_requests_total', 'Total requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('medgemma_request_duration_seconds', 'Request duration')
MODEL_INFERENCE_DURATION = Histogram('medgemma_inference_duration_seconds', 'Model inference duration')

# Global service instance
medgemma_service: Optional[MedGemmaService] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global medgemma_service
    
    logger.info("Starting MedGemma service...")
    try:
        medgemma_service = MedGemmaService()
        await medgemma_service.initialize()
        logger.info("MedGemma service initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize MedGemma service: {e}")
        raise
    
    yield
    
    logger.info("Shutting down MedGemma service...")
    if medgemma_service:
        await medgemma_service.cleanup()

# Create FastAPI app
app = FastAPI(
    title="MedGemma AI Chat API",
    description="Production-ready AI chat service using Google's MedGemma-4b-it model",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Security
security = HTTPBearer()

# Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)

# Exception handlers
app.add_exception_handler(MedGemmaException, handle_medgemma_exception)

@app.middleware("http")
async def add_process_time_header(request, call_next):
    """Add request processing time to response headers"""
    import time
    start_time = time.time()
    
    # Count request
    REQUEST_COUNT.labels(method=request.method, endpoint=request.url.path).inc()
    
    response = await call_next(request)
    
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    
    # Record duration
    REQUEST_DURATION.observe(process_time)
    
    return response

# Health check endpoint
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        if not medgemma_service or not medgemma_service.is_ready():
            raise HTTPException(status_code=503, detail="Service not ready")
        
        return HealthResponse(
            status="healthy",
            model_loaded=medgemma_service.is_model_loaded(),
            version="1.0.0"
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")

@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)

# Chat endpoints
@app.post("/chat", response_model=ChatResponse)
async def chat(
    request: ChatRequest,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Process chat message"""
    verify_api_key(credentials.credentials)
    
    if not medgemma_service:
        raise HTTPException(status_code=503, detail="Service not available")
    
    try:
        conversation_id = request.conversation_id or create_conversation_id()
        
        with MODEL_INFERENCE_DURATION.time():
            response = await medgemma_service.generate_response(
                message=request.message,
                conversation_id=conversation_id,
                max_length=request.max_length,
                temperature=request.temperature,
                top_p=request.top_p
            )
        
        return ChatResponse(
            response=response,
            conversation_id=conversation_id,
            model="medgemma-4b-it"
        )
    
    except Exception as e:
        logger.error(f"Chat request failed: {e}")
        raise MedGemmaException(f"Failed to process chat request: {str(e)}")

@app.post("/chat/stream")
async def chat_stream(
    request: ChatRequest,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Stream chat response"""
    verify_api_key(credentials.credentials)
    
    if not medgemma_service:
        raise HTTPException(status_code=503, detail="Service not available")
    
    conversation_id = request.conversation_id or create_conversation_id()
    
    async def generate():
        try:
            async for chunk in medgemma_service.generate_response_stream(
                message=request.message,
                conversation_id=conversation_id,
                max_length=request.max_length,
                temperature=request.temperature,
                top_p=request.top_p
            ):
                yield f"data: {chunk}\n\n"
        except Exception as e:
            logger.error(f"Streaming failed: {e}")
            yield f"data: {{'error': 'Streaming failed: {str(e)}'}}\n\n"
        finally:
            yield "data: [DONE]\n\n"
    
    return StreamingResponse(
        generate(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Conversation-ID": conversation_id
        }
    )

@app.post("/analyze-image", response_model=ChatResponse)
async def analyze_image(
    image: UploadFile = File(...),
    message: str = Form(...),
    conversation_id: Optional[str] = Form(None),
    max_length: int = Form(2048),
    temperature: float = Form(0.7),
    top_p: float = Form(0.9),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Analyze medical image with text prompt"""
    verify_api_key(credentials.credentials)
    
    if not medgemma_service:
        raise HTTPException(status_code=503, detail="Service not available")
    
    # Validate file type
    if not image.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    # Check file size
    if image.size > settings.UPLOAD_MAX_SIZE:
        raise HTTPException(status_code=413, detail="File too large")
    
    try:
        conversation_id = conversation_id or create_conversation_id()
        
        # Read image data
        image_data = await image.read()
        
        with MODEL_INFERENCE_DURATION.time():
            response = await medgemma_service.analyze_image(
                image_data=image_data,
                message=message,
                conversation_id=conversation_id,
                max_length=max_length,
                temperature=temperature,
                top_p=top_p
            )
        
        return ChatResponse(
            response=response,
            conversation_id=conversation_id,
            model="medgemma-4b-it"
        )
    
    except Exception as e:
        logger.error(f"Image analysis failed: {e}")
        raise MedGemmaException(f"Failed to analyze image: {str(e)}")

@app.get("/conversations/{conversation_id}")
async def get_conversation(
    conversation_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Get conversation history"""
    verify_api_key(credentials.credentials)
    
    if not medgemma_service:
        raise HTTPException(status_code=503, detail="Service not available")
    
    try:
        history = await medgemma_service.get_conversation_history(conversation_id)
        return {"conversation_id": conversation_id, "history": history}
    except Exception as e:
        logger.error(f"Failed to get conversation: {e}")
        raise HTTPException(status_code=404, detail="Conversation not found")

@app.delete("/conversations/{conversation_id}")
async def delete_conversation(
    conversation_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Delete conversation history"""
    verify_api_key(credentials.credentials)
    
    if not medgemma_service:
        raise HTTPException(status_code=503, detail="Service not available")
    
    try:
        await medgemma_service.delete_conversation(conversation_id)
        return {"message": "Conversation deleted successfully"}
    except Exception as e:
        logger.error(f"Failed to delete conversation: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete conversation")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        workers=1
    )
