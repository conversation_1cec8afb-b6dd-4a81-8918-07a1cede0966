# Test Dockerfile for MedGemma AI Chat
# Uses minimal dependencies for fast testing without AI model downloads

FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    USE_MOCK_MODEL=true \
    ENVIRONMENT=test

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy test requirements and install
COPY requirements-test.txt .
RUN pip install --no-cache-dir -r requirements-test.txt

# Copy application code
COPY app/ ./app/
COPY scripts/mock-model.py ./scripts/

# Create necessary directories
RUN mkdir -p /app/model_cache /app/uploads /app/logs

# Create a simple test application
RUN cat > /app/app/main_test.py << 'EOF'
import os
import asyncio
import json
from datetime import datetime
from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse, StreamingResponse
import structlog

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Create FastAPI app
app = FastAPI(
    title="MedGemma AI Chat Test API",
    description="Test version with mock responses",
    version="1.0.0-test"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mock responses
MOCK_RESPONSES = [
    "This is a mock response for testing the MedGemma AI Chat application.",
    "Mock medical AI: Please consult with healthcare professionals for real medical advice.",
    "Test response: This demonstrates API functionality without the actual AI model.",
    "Mock diagnosis: This is a simulated response for testing purposes only.",
    "Test medical advice: This is a mock response to validate the application structure."
]

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "model_loaded": True,
        "version": "1.0.0-test",
        "timestamp": datetime.utcnow().isoformat(),
        "test_mode": True
    }

@app.get("/metrics")
async def get_metrics():
    """Prometheus metrics endpoint"""
    return {
        "test_mode": True,
        "requests_total": 42,
        "response_time_seconds": 0.1,
        "model_loaded": True,
        "timestamp": datetime.utcnow().isoformat()
    }

@app.post("/chat")
async def chat(request: dict):
    """Mock chat endpoint"""
    await asyncio.sleep(0.1)  # Simulate processing time
    
    message = request.get("message", "")
    conversation_id = request.get("conversation_id", f"test-conv-{datetime.utcnow().timestamp()}")
    
    # Select mock response based on message content
    import random
    response = random.choice(MOCK_RESPONSES)
    
    if "emergency" in message.lower():
        response = "Mock emergency response: If this is a medical emergency, please contact emergency services immediately."
    elif "symptom" in message.lower():
        response = "Mock symptom analysis: This is a test response. Please consult with healthcare professionals."
    
    return {
        "response": f"{response} (Input: '{message[:50]}...')",
        "conversation_id": conversation_id,
        "model": "mock-medgemma-test",
        "timestamp": datetime.utcnow().isoformat(),
        "usage": {
            "prompt_tokens": len(message.split()),
            "completion_tokens": len(response.split()),
            "total_tokens": len(message.split()) + len(response.split())
        }
    }

@app.post("/analyze-image")
async def analyze_image(
    message: str = Form(...),
    image: UploadFile = File(...),
    conversation_id: str = Form(None)
):
    """Mock image analysis endpoint"""
    await asyncio.sleep(0.2)  # Simulate image processing time
    
    # Read image data for size calculation
    image_data = await image.read()
    image_size = len(image_data)
    
    conv_id = conversation_id or f"test-conv-{datetime.utcnow().timestamp()}"
    
    response = f"Mock image analysis: Processed {image_size} bytes of image data. User query: '{message}'. This is a test response for image analysis functionality."
    
    return {
        "response": response,
        "conversation_id": conv_id,
        "model": "mock-medgemma-test",
        "timestamp": datetime.utcnow().isoformat(),
        "image_info": {
            "filename": image.filename,
            "content_type": image.content_type,
            "size_bytes": image_size
        }
    }

@app.post("/chat/stream")
async def chat_stream(request: dict):
    """Mock streaming chat endpoint"""
    message = request.get("message", "")
    conversation_id = request.get("conversation_id", f"test-conv-{datetime.utcnow().timestamp()}")
    
    async def generate():
        response = f"Mock streaming response to: {message}"
        words = response.split()
        
        for i, word in enumerate(words):
            chunk_data = {
                "chunk": word + (" " if i < len(words) - 1 else ""),
                "conversation_id": conversation_id,
                "is_complete": i == len(words) - 1
            }
            yield f"data: {json.dumps(chunk_data)}\n\n"
            await asyncio.sleep(0.1)
        
        yield "data: [DONE]\n\n"
    
    return StreamingResponse(
        generate(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive"
        }
    )

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "MedGemma AI Chat Test API",
        "version": "1.0.0-test",
        "test_mode": True,
        "endpoints": {
            "health": "/health",
            "chat": "/chat",
            "analyze_image": "/analyze-image",
            "stream": "/chat/stream",
            "metrics": "/metrics"
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
EOF

# Create non-root user
RUN groupadd -r medgemma && useradd -r -g medgemma medgemma && \
    chown -R medgemma:medgemma /app

USER medgemma

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Start the test application
CMD ["python", "-m", "uvicorn", "app.main_test:app", "--host", "0.0.0.0", "--port", "8000"]
