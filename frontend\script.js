// MedGemma Chat Interface JavaScript

class MedGemmaChat {
    constructor() {
        this.apiKey = this.getApiKey();
        this.conversationId = this.generateConversationId();
        this.currentImage = null;
        this.isProcessing = false;
        
        this.initializeElements();
        this.attachEventListeners();
        this.checkApiStatus();
    }
    
    initializeElements() {
        // Chat elements
        this.chatMessages = document.getElementById('chat-messages');
        this.messageInput = document.getElementById('message-input');
        this.sendBtn = document.getElementById('send-btn');
        this.chatForm = document.getElementById('chat-form');
        
        // Image elements
        this.imageInput = document.getElementById('image-input');
        this.imageBtn = document.getElementById('image-btn');
        this.imagePreview = document.getElementById('image-preview');
        this.previewImage = document.getElementById('preview-image');
        this.removeImageBtn = document.getElementById('remove-image');
        
        // Settings elements
        this.settingsToggle = document.getElementById('settings-toggle');
        this.settingsContent = document.getElementById('settings-content');
        this.temperatureSlider = document.getElementById('temperature');
        this.tempValue = document.getElementById('temp-value');
        this.maxLengthSlider = document.getElementById('max-length');
        this.lengthValue = document.getElementById('length-value');
        this.clearBtn = document.getElementById('clear-chat');
        
        // Status elements
        this.statusDot = document.getElementById('status-dot');
        this.statusText = document.getElementById('status-text');
        
        // Modal elements
        this.loadingOverlay = document.getElementById('loading-overlay');
        this.errorModal = document.getElementById('error-modal');
        this.errorMessage = document.getElementById('error-message');
    }
    
    attachEventListeners() {
        // Chat form submission
        this.chatForm.addEventListener('submit', (e) => this.handleSubmit(e));
        
        // Input validation
        this.messageInput.addEventListener('input', () => this.validateInput());
        
        // Image upload
        this.imageBtn.addEventListener('click', () => this.imageInput.click());
        this.imageInput.addEventListener('change', (e) => this.handleImageUpload(e));
        this.removeImageBtn.addEventListener('click', () => this.removeImage());
        
        // Settings
        this.settingsToggle.addEventListener('click', () => this.toggleSettings());
        this.temperatureSlider.addEventListener('input', (e) => {
            this.tempValue.textContent = e.target.value;
        });
        this.maxLengthSlider.addEventListener('input', (e) => {
            this.lengthValue.textContent = e.target.value;
        });
        this.clearBtn.addEventListener('click', () => this.clearChat());
        
        // Modal close
        document.querySelector('.close').addEventListener('click', () => {
            this.errorModal.style.display = 'none';
        });
        
        // Click outside modal to close
        this.errorModal.addEventListener('click', (e) => {
            if (e.target === this.errorModal) {
                this.errorModal.style.display = 'none';
            }
        });
    }
    
    getApiKey() {
        // Check for development mode
        if (window.location.hostname === 'localhost' || window.location.hostname.match(/^\d+\.\d+\.\d+\.\d+$/)) {
            // Development mode - use development API key
            return localStorage.getItem('medgemma_api_key') || 'dev-api-key-for-testing-only';
        }
        // Production mode - use secure API key
        return localStorage.getItem('medgemma_api_key') || 'your-secret-api-key';
    }

    getApiBaseUrl() {
        // Automatically detect the base URL based on current location
        const protocol = window.location.protocol;
        const hostname = window.location.hostname;
        const port = window.location.port;

        // For development, use HTTP and current host
        if (hostname === 'localhost' || hostname.match(/^\d+\.\d+\.\d+\.\d+$/)) {
            return `${protocol}//${hostname}${port ? ':' + port : ''}/api`;
        }

        // For production, use HTTPS
        return `https://${hostname}/api`;
    }
    
    generateConversationId() {
        return 'conv_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    }
    
    async checkApiStatus() {
        try {
            const baseUrl = this.getApiBaseUrl();
            const response = await fetch(`${baseUrl}/health`);
            if (response.ok) {
                this.updateStatus('connected', 'Connected');
            } else {
                this.updateStatus('error', 'Service Error');
            }
        } catch (error) {
            this.updateStatus('error', 'Connection Failed');
            console.error('API Status Check Error:', error);
        }
    }
    
    updateStatus(status, text) {
        this.statusDot.className = `status-dot ${status}`;
        this.statusText.textContent = text;
    }
    
    validateInput() {
        const hasText = this.messageInput.value.trim().length > 0;
        const hasImage = this.currentImage !== null;
        this.sendBtn.disabled = !hasText && !hasImage || this.isProcessing;
    }
    
    async handleSubmit(e) {
        e.preventDefault();
        
        if (this.isProcessing) return;
        
        const message = this.messageInput.value.trim();
        if (!message && !this.currentImage) return;
        
        this.isProcessing = true;
        this.showLoading();
        
        try {
            // Add user message to chat
            this.addMessage('user', message, this.currentImage);
            
            // Clear input
            this.messageInput.value = '';
            this.removeImage();
            this.validateInput();
            
            // Send request
            let response;
            if (this.currentImage) {
                response = await this.sendImageMessage(message, this.currentImage);
            } else {
                response = await this.sendTextMessage(message);
            }
            
            // Add assistant response
            this.addMessage('assistant', response.response);
            
        } catch (error) {
            this.showError(error.message);
        } finally {
            this.isProcessing = false;
            this.hideLoading();
            this.validateInput();
        }
    }
    
    async sendTextMessage(message) {
        const baseUrl = this.getApiBaseUrl();
        const response = await fetch(`${baseUrl}/chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.apiKey}`
            },
            body: JSON.stringify({
                message: message,
                conversation_id: this.conversationId,
                temperature: parseFloat(this.temperatureSlider.value),
                max_length: parseInt(this.maxLengthSlider.value)
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Failed to send message');
        }

        return await response.json();
    }
    
    async sendImageMessage(message, imageFile) {
        const baseUrl = this.getApiBaseUrl();
        const formData = new FormData();
        formData.append('message', message);
        formData.append('image', imageFile);
        formData.append('conversation_id', this.conversationId);
        formData.append('temperature', this.temperatureSlider.value);
        formData.append('max_length', this.maxLengthSlider.value);

        const response = await fetch(`${baseUrl}/analyze-image`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.apiKey}`
            },
            body: formData
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Failed to analyze image');
        }

        return await response.json();
    }
    
    handleImageUpload(e) {
        const file = e.target.files[0];
        if (!file) return;
        
        // Validate file type
        if (!file.type.startsWith('image/')) {
            this.showError('Please select a valid image file');
            return;
        }
        
        // Validate file size (10MB limit)
        if (file.size > 10 * 1024 * 1024) {
            this.showError('Image file too large. Maximum size is 10MB');
            return;
        }
        
        this.currentImage = file;
        
        // Show preview
        const reader = new FileReader();
        reader.onload = (e) => {
            this.previewImage.src = e.target.result;
            this.imagePreview.style.display = 'block';
            this.validateInput();
        };
        reader.readAsDataURL(file);
    }
    
    removeImage() {
        this.currentImage = null;
        this.imagePreview.style.display = 'none';
        this.imageInput.value = '';
        this.validateInput();
    }
    
    addMessage(role, content, imageFile = null) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = role === 'user' ? '👤' : '🤖';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        // Add image if present
        if (imageFile && role === 'user') {
            const img = document.createElement('img');
            img.className = 'message-image';
            img.src = URL.createObjectURL(imageFile);
            messageContent.appendChild(img);
        }
        
        // Add text content
        if (content) {
            const textDiv = document.createElement('div');
            textDiv.textContent = content;
            messageContent.appendChild(textDiv);
        }
        
        // Add timestamp
        const timeDiv = document.createElement('div');
        timeDiv.className = 'message-time';
        timeDiv.textContent = new Date().toLocaleTimeString();
        messageContent.appendChild(timeDiv);
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);
        
        // Remove welcome message if it exists
        const welcomeMessage = this.chatMessages.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }
    
    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }
    
    toggleSettings() {
        const isVisible = this.settingsContent.style.display !== 'none';
        this.settingsContent.style.display = isVisible ? 'none' : 'block';
    }
    
    clearChat() {
        // Remove all messages except welcome message
        const messages = this.chatMessages.querySelectorAll('.message');
        messages.forEach(msg => msg.remove());
        
        // Add welcome message back
        this.addWelcomeMessage();
        
        // Generate new conversation ID
        this.conversationId = this.generateConversationId();
    }
    
    addWelcomeMessage() {
        const welcomeDiv = document.createElement('div');
        welcomeDiv.className = 'welcome-message';
        welcomeDiv.innerHTML = `
            <div class="welcome-icon">🤖</div>
            <h2>Welcome to MedGemma AI</h2>
            <p>I'm an AI assistant specialized in medical image analysis and healthcare questions. You can:</p>
            <ul>
                <li>Ask medical questions</li>
                <li>Upload medical images for analysis</li>
                <li>Get explanations about medical conditions</li>
            </ul>
            <p class="disclaimer">
                <strong>Disclaimer:</strong> This AI is for educational purposes only. 
                Always consult with qualified healthcare professionals for medical advice.
            </p>
        `;
        this.chatMessages.appendChild(welcomeDiv);
    }
    
    showLoading() {
        this.loadingOverlay.style.display = 'flex';
    }
    
    hideLoading() {
        this.loadingOverlay.style.display = 'none';
    }
    
    showError(message) {
        this.errorMessage.textContent = message;
        this.errorModal.style.display = 'flex';
    }
}

// Initialize the chat application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new MedGemmaChat();
});
