#!/usr/bin/env python3
"""
Mock Model for MedGemma AI Chat Testing

This script provides a lightweight mock implementation of the MedGemma model
for testing purposes, avoiding the need to download the actual 4GB+ model.
"""

import asyncio
import json
import random
import time
from typing import List, Dict, Any, Optional, AsyncGenerator
from datetime import datetime
import logging

# Mock responses for different types of medical queries
MOCK_RESPONSES = {
    "general": [
        "This is a mock response for testing purposes. In a real deployment, this would be generated by the MedGemma-4b-it model.",
        "Mock medical AI response: Please consult with a healthcare professional for actual medical advice.",
        "Test response: This demonstrates the API functionality without the actual AI model.",
    ],
    "symptoms": [
        "Mock response: Based on the symptoms described, I would recommend consulting with a healthcare provider for proper evaluation.",
        "Test medical advice: The symptoms you've mentioned could have various causes. Please seek professional medical attention.",
        "Mock diagnosis: This is a simulated response. Real medical evaluation requires professional assessment.",
    ],
    "image_analysis": [
        "Mock image analysis: This appears to be a medical image. In production, the MedGemma model would provide detailed analysis.",
        "Test radiology report: Image received and processed. This is a mock analysis for testing purposes.",
        "Simulated medical imaging response: The uploaded image has been analyzed using mock processing.",
    ],
    "medication": [
        "Mock medication information: Please consult with a pharmacist or healthcare provider for accurate medication guidance.",
        "Test drug information: This is simulated pharmaceutical advice. Always verify with medical professionals.",
        "Mock prescription guidance: Medication questions should be directed to qualified healthcare providers.",
    ],
    "emergency": [
        "Mock emergency response: If this is a medical emergency, please contact emergency services immediately.",
        "Test urgent care advice: For actual emergencies, call your local emergency number (911, 999, etc.).",
        "Simulated emergency guidance: This is a test response. Seek immediate medical attention for real emergencies.",
    ]
}

class MockMedGemmaModel:
    """Mock implementation of the MedGemma model for testing"""
    
    def __init__(self):
        self.model_name = "mock/medgemma-test-model"
        self.is_loaded = False
        self.load_time = 0
        self.inference_count = 0
        
        # Set up logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    async def initialize(self):
        """Mock model initialization"""
        self.logger.info("Initializing mock MedGemma model...")
        
        # Simulate model loading time (much faster than real model)
        await asyncio.sleep(2)
        
        self.is_loaded = True
        self.load_time = time.time()
        
        self.logger.info("Mock MedGemma model initialized successfully")
    
    def is_ready(self) -> bool:
        """Check if mock model is ready"""
        return self.is_loaded
    
    def _classify_query(self, message: str) -> str:
        """Classify the type of medical query for appropriate mock response"""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["emergency", "urgent", "911", "help", "pain", "chest"]):
            return "emergency"
        elif any(word in message_lower for word in ["symptom", "feel", "hurt", "ache", "sick"]):
            return "symptoms"
        elif any(word in message_lower for word in ["medication", "drug", "pill", "prescription", "dose"]):
            return "medication"
        elif any(word in message_lower for word in ["image", "scan", "x-ray", "mri", "ct", "ultrasound"]):
            return "image_analysis"
        else:
            return "general"
    
    async def generate_response(
        self,
        message: str,
        conversation_id: str,
        max_length: int = 2048,
        temperature: float = 0.7,
        top_p: float = 0.9
    ) -> str:
        """Generate mock text response"""
        
        if not self.is_ready():
            raise RuntimeError("Mock model not initialized")
        
        # Simulate processing time
        processing_time = random.uniform(0.5, 2.0)
        await asyncio.sleep(processing_time)
        
        # Classify query and get appropriate response
        query_type = self._classify_query(message)
        responses = MOCK_RESPONSES.get(query_type, MOCK_RESPONSES["general"])
        base_response = random.choice(responses)
        
        # Add some variation based on parameters
        if temperature > 0.8:
            variation = " (High creativity mode - more varied response)"
        elif temperature < 0.3:
            variation = " (Low creativity mode - focused response)"
        else:
            variation = ""
        
        # Construct full response
        response = f"{base_response}{variation}"
        
        # Simulate max_length constraint
        if len(response) > max_length:
            response = response[:max_length-3] + "..."
        
        self.inference_count += 1
        
        self.logger.info(f"Generated mock response for query type: {query_type}")
        
        return response
    
    async def analyze_image(
        self,
        image_data: bytes,
        message: str,
        conversation_id: str,
        max_length: int = 2048,
        temperature: float = 0.7,
        top_p: float = 0.9
    ) -> str:
        """Generate mock image analysis response"""
        
        if not self.is_ready():
            raise RuntimeError("Mock model not initialized")
        
        # Simulate image processing time
        processing_time = random.uniform(1.0, 3.0)
        await asyncio.sleep(processing_time)
        
        # Get image analysis response
        responses = MOCK_RESPONSES["image_analysis"]
        base_response = random.choice(responses)
        
        # Add image metadata simulation
        image_size = len(image_data)
        image_info = f" Image size: {image_size} bytes."
        
        # Combine response
        response = f"{base_response}{image_info}"
        
        # Add user message context
        if message:
            response += f" User query: '{message}'"
        
        # Simulate max_length constraint
        if len(response) > max_length:
            response = response[:max_length-3] + "..."
        
        self.inference_count += 1
        
        self.logger.info(f"Generated mock image analysis for {image_size} byte image")
        
        return response
    
    async def generate_response_stream(
        self,
        message: str,
        conversation_id: str,
        max_length: int = 2048,
        temperature: float = 0.7,
        top_p: float = 0.9
    ) -> AsyncGenerator[str, None]:
        """Generate mock streaming response"""
        
        if not self.is_ready():
            raise RuntimeError("Mock model not initialized")
        
        # Get full response first
        full_response = await self.generate_response(
            message, conversation_id, max_length, temperature, top_p
        )
        
        # Stream it word by word
        words = full_response.split()
        for i, word in enumerate(words):
            chunk_data = {
                "chunk": word + (" " if i < len(words) - 1 else ""),
                "conversation_id": conversation_id,
                "is_complete": i == len(words) - 1
            }
            
            yield json.dumps(chunk_data)
            
            # Simulate streaming delay
            await asyncio.sleep(0.1)
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get mock model information"""
        return {
            "name": self.model_name,
            "type": "mock",
            "is_loaded": self.is_loaded,
            "load_time": self.load_time,
            "inference_count": self.inference_count,
            "capabilities": [
                "text_generation",
                "image_analysis",
                "medical_qa",
                "streaming_response"
            ],
            "max_context_length": 2048,
            "supported_languages": ["en"],
            "version": "1.0.0-mock"
        }
    
    async def cleanup(self):
        """Cleanup mock model resources"""
        self.logger.info("Cleaning up mock MedGemma model...")
        self.is_loaded = False
        self.inference_count = 0
        self.logger.info("Mock model cleanup completed")


# Global mock model instance
mock_model = MockMedGemmaModel()


async def test_mock_model():
    """Test the mock model functionality"""
    print("Testing Mock MedGemma Model")
    print("=" * 40)
    
    # Initialize
    await mock_model.initialize()
    print(f"✅ Model initialized: {mock_model.is_ready()}")
    
    # Test text generation
    response = await mock_model.generate_response(
        "What are the symptoms of diabetes?",
        "test-conversation-1"
    )
    print(f"✅ Text response: {response[:100]}...")
    
    # Test image analysis
    fake_image_data = b"fake_image_data_for_testing"
    image_response = await mock_model.analyze_image(
        fake_image_data,
        "Analyze this medical image",
        "test-conversation-2"
    )
    print(f"✅ Image analysis: {image_response[:100]}...")
    
    # Test streaming
    print("✅ Streaming test:")
    async for chunk in mock_model.generate_response_stream(
        "Hello, how are you?",
        "test-conversation-3"
    ):
        chunk_data = json.loads(chunk)
        print(f"   Chunk: {chunk_data['chunk']}", end="")
    print()
    
    # Get model info
    info = mock_model.get_model_info()
    print(f"✅ Model info: {info['name']}, Inferences: {info['inference_count']}")
    
    # Cleanup
    await mock_model.cleanup()
    print("✅ Cleanup completed")


if __name__ == "__main__":
    # Run test if script is executed directly
    asyncio.run(test_mock_model())
