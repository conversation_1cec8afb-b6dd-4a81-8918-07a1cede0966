/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Header */
.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.title {
    font-size: 1.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.icon {
    font-size: 2rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #ff4757;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: #2ed573;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.chat-messages {
    max-width: 800px;
    margin: 0 auto;
}

/* Welcome Message */
.welcome-message {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border-radius: 15px;
    margin-bottom: 2rem;
}

.welcome-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.welcome-message h2 {
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.welcome-message ul {
    text-align: left;
    max-width: 400px;
    margin: 1rem auto;
}

.welcome-message li {
    margin: 0.5rem 0;
}

.disclaimer {
    margin-top: 1rem;
    font-size: 0.9rem;
    opacity: 0.9;
    font-style: italic;
}

/* Chat Messages */
.message {
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.message.assistant .message-avatar {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.message-content {
    max-width: 70%;
    padding: 1rem;
    border-radius: 15px;
    word-wrap: break-word;
}

.message.user .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom-right-radius: 5px;
}

.message.assistant .message-content {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-bottom-left-radius: 5px;
}

.message-image {
    max-width: 200px;
    border-radius: 10px;
    margin-bottom: 0.5rem;
}

.message-time {
    font-size: 0.8rem;
    opacity: 0.7;
    margin-top: 0.5rem;
}

/* Input Area */
.input-area {
    padding: 1rem 2rem;
    background: white;
    border-top: 1px solid #e9ecef;
}

.image-preview {
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
}

.image-preview img {
    max-width: 200px;
    max-height: 150px;
    border-radius: 10px;
    border: 2px solid #4facfe;
}

.remove-image-btn {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    background: #ff4757;
    color: white;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-form {
    max-width: 800px;
    margin: 0 auto;
}

.input-container {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 0.5rem;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s;
}

.input-container:focus-within {
    border-color: #4facfe;
}

.image-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    cursor: pointer;
    font-size: 1.2rem;
    transition: transform 0.2s;
}

.image-btn:hover {
    transform: scale(1.1);
}

.message-input {
    flex: 1;
    border: none;
    outline: none;
    padding: 0.75rem;
    font-size: 1rem;
    background: transparent;
}

.send-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    cursor: pointer;
    transition: transform 0.2s;
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.send-btn:not(:disabled):hover {
    transform: scale(1.1);
}

.send-icon {
    font-size: 1.2rem;
}

/* Settings Panel */
.settings-panel {
    margin-top: 1rem;
    text-align: center;
}

.settings-toggle {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background 0.3s;
}

.settings-toggle:hover {
    background: #f8f9fa;
}

.settings-content {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    margin-top: 0.5rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.setting-group {
    margin-bottom: 1rem;
}

.setting-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.setting-group input[type="range"] {
    width: 100%;
}

.clear-btn {
    background: #ff4757;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s;
}

.clear-btn:hover {
    background: #ff3838;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    color: white;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-top: 5px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;
}

.modal-content {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    max-width: 500px;
    width: 90%;
    position: relative;
}

.close {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        height: 100vh;
    }
    
    .header {
        padding: 1rem;
    }
    
    .title {
        font-size: 1.4rem;
    }
    
    .input-area {
        padding: 1rem;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .welcome-message {
        padding: 1rem;
    }
}
